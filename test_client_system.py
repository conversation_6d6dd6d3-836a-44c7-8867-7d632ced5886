"""
测试完整的客户端系统
包括模型上传、加密、客户端生成、注册码生成等
"""
import os
import sys
import tempfile
import shutil
import json
import base64
from datetime import datetime, timedelta

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def create_test_fmw():
    """创建测试FMW文件"""
    fmw_content = '''#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#!   TITLE="客户端测试模型"
#!   DESCRIPTION="用于测试客户端系统的FMW文件"
#!   CATEGORY="测试"
#! >
#! 
#! <USER_PARAMETERS>
#!   <USER_PARAMETER
#!     GUI_LINE="INPUT_FILE|文件|文件|"
#!     DEFAULT_VALUE=""
#!     IS_STAND_ALONE="true"
#!     PARM_NAME="INPUT_FILE"
#!     PROMPT="输入文件:"
#!     TYPE="FILENAME_MUSTEXIST"
#!   />
#!   <USER_PARAMETER
#!     GUI_LINE="OUTPUT_DIR|文件夹|文件夹|"
#!     DEFAULT_VALUE=""
#!     IS_STAND_ALONE="true"
#!     PARM_NAME="OUTPUT_DIR"
#!     PROMPT="输出目录:"
#!     TYPE="DIRNAME"
#!   />
#! </USER_PARAMETERS>
#! 
#! </WORKSPACE>'''
    
    return fmw_content

def test_complete_workflow():
    """测试完整的工作流程"""
    print("=" * 60)
    print("测试完整的客户端系统工作流程")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 设置临时配置
        from config import config
        original_models_dir = config.get("models_dir")
        original_clients_dir = config.get("clients_dir")
        
        models_temp_dir = os.path.join(temp_dir, "models")
        clients_temp_dir = os.path.join(temp_dir, "clients")
        
        config.set("models_dir", models_temp_dir)
        config.set("clients_dir", clients_temp_dir)
        
        print("1. 创建和导入测试模型...")
        
        # 创建测试FMW文件
        test_fmw_content = create_test_fmw()
        test_fmw_path = os.path.join(temp_dir, "client_test_model.fmw")
        
        with open(test_fmw_path, 'w', encoding='utf-8') as f:
            f.write(test_fmw_content)
        
        print(f"✓ 创建测试FMW文件: {test_fmw_path}")
        
        # 导入模型（自动加密）
        from model_manager import ModelManager
        
        manager = ModelManager()
        
        success, message, model_id = manager.import_model(
            test_fmw_path,
            "客户端测试模型",
            "用于测试客户端系统的模型",
            "测试分类",
            auto_encrypt=True  # 自动加密
        )
        
        if success:
            print(f"✓ 模型导入成功: {model_id}")
            
            model_info = manager.get_model_info(model_id)
            print(f"  加密状态: {'已加密' if model_info['is_encrypted'] else '未加密'}")
        else:
            print(f"✗ 模型导入失败: {message}")
            return False
        
        print("\n2. 生成客户端...")
        
        # 生成客户端
        from client_generator import ClientGenerator
        
        generator = ClientGenerator()
        
        success, message, zip_path = generator.generate_client(
            "测试客户端",
            [model_id],  # 包含测试模型
            {
                "expire_days": 30,
                "max_uses": 100
            }
        )
        
        if success:
            print(f"✓ 客户端生成成功: {zip_path}")
            
            # 验证客户端内容
            import zipfile
            with zipfile.ZipFile(zip_path, 'r') as zf:
                file_list = zf.namelist()
                
                required_files = [
                    "client_template.py",
                    "models/",
                    "license.json"
                ]
                
                for req_file in required_files:
                    matching_files = [f for f in file_list if req_file in f]
                    if matching_files:
                        print(f"  ✓ 包含: {req_file}")
                    else:
                        print(f"  ✗ 缺少: {req_file}")
                
                # 检查加密模型
                encrypted_models = [f for f in file_list if f.endswith('.encrypted')]
                if encrypted_models:
                    print(f"  ✓ 包含加密模型: {encrypted_models}")
                else:
                    print("  ⚠️ 未找到加密模型文件")
        else:
            print(f"✗ 客户端生成失败: {message}")
            return False
        
        print("\n3. 测试注册码生成...")
        
        # 模拟机器码
        import hashlib
        import platform
        
        test_machine_info = f"TestMachine-{platform.machine()}-TestProcessor"
        test_machine_id = hashlib.md5(test_machine_info.encode()).hexdigest()
        
        print(f"  测试机器码: {test_machine_id}")
        
        # 生成注册码
        license_data = {
            "license_id": "test-license-001",
            "client_name": "测试客户端",
            "created_at": datetime.now().isoformat(),
            "allowed_machines": [test_machine_id],
            "current_uses": 0,
            "is_active": True,
            "expire_date": (datetime.now() + timedelta(days=30)).isoformat(),
            "max_uses": 100
        }
        
        license_json = json.dumps(license_data, ensure_ascii=False)
        registration_code = base64.b64encode(license_json.encode()).decode()
        
        print(f"✓ 注册码生成成功")
        print(f"  注册码长度: {len(registration_code)} 字符")
        print(f"  注册码前50字符: {registration_code[:50]}...")
        
        print("\n4. 测试注册码验证...")
        
        # 验证注册码
        try:
            decoded_data = base64.b64decode(registration_code.encode())
            decoded_license = json.loads(decoded_data.decode())
            
            # 验证字段
            required_fields = ["license_id", "client_name", "allowed_machines"]
            for field in required_fields:
                if field in decoded_license:
                    print(f"  ✓ 包含字段: {field}")
                else:
                    print(f"  ✗ 缺少字段: {field}")
            
            # 验证机器码
            if test_machine_id in decoded_license.get("allowed_machines", []):
                print(f"  ✓ 机器码验证通过")
            else:
                print(f"  ✗ 机器码验证失败")
            
        except Exception as e:
            print(f"  ✗ 注册码验证失败: {e}")
            return False
        
        print("\n5. 测试客户端模板...")
        
        # 测试客户端模板导入
        try:
            from client_template import FMEClient
            print("  ✓ 客户端模板导入成功")
        except Exception as e:
            print(f"  ✗ 客户端模板导入失败: {e}")
            return False
        
        print("\n6. 生成完整的客户端包...")
        
        # 创建完整的客户端目录结构
        client_test_dir = os.path.join(temp_dir, "client_test")
        os.makedirs(client_test_dir, exist_ok=True)
        
        # 复制客户端模板
        shutil.copy2("client_template.py", os.path.join(client_test_dir, "main.py"))
        
        # 创建models目录并复制加密模型
        client_models_dir = os.path.join(client_test_dir, "models")
        os.makedirs(client_models_dir, exist_ok=True)
        
        if model_info['is_encrypted']:
            encrypted_file = model_info['encrypted_file_path']
            if os.path.exists(encrypted_file):
                shutil.copy2(encrypted_file, 
                           os.path.join(client_models_dir, "客户端测试模型.fmw.encrypted"))
                print("  ✓ 复制加密模型到客户端")
        
        # 创建启动脚本
        start_script = '''@echo off
chcp 65001 >nul
title FME客户端

echo 启动FME模型运行客户端...
python main.py

if errorlevel 1 (
    echo 程序异常退出
    pause
)
'''
        
        with open(os.path.join(client_test_dir, "start.bat"), 'w', encoding='utf-8') as f:
            f.write(start_script)
        
        print(f"  ✓ 客户端包创建完成: {client_test_dir}")
        
        # 恢复原始配置
        config.set("models_dir", original_models_dir)
        config.set("clients_dir", original_clients_dir)
        
        print("\n" + "=" * 60)
        print("完整工作流程测试成功！")
        print("=" * 60)
        
        print(f"\n📁 测试结果:")
        print(f"  • 测试目录: {temp_dir}")
        print(f"  • 客户端包: {client_test_dir}")
        print(f"  • 注册码: {registration_code[:50]}...")
        
        print(f"\n🎯 工作流程总结:")
        print(f"  1. ✅ FMW模型上传到相对路径")
        print(f"  2. ✅ 模型自动加密保护")
        print(f"  3. ✅ 客户端自动生成")
        print(f"  4. ✅ 注册码生成和验证")
        print(f"  5. ✅ 客户端包含所需功能")
        
        print(f"\n🚀 客户端功能:")
        print(f"  • 自动检测机器码")
        print(f"  • 注册码输入激活")
        print(f"  • 模型运行管理")
        print(f"  • 运行日志查看")
        print(f"  • 加密模型支持")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 注意：不清理临时目录，以便查看结果
        print(f"\n💡 提示: 测试文件保留在 {temp_dir}")
        print("   可以手动查看生成的客户端和模型文件")

def test_registration_code_generator():
    """测试注册码生成器"""
    print("\n7. 测试注册码生成器...")
    
    try:
        from registration_code_generator import RegistrationCodeGenerator
        print("  ✓ 注册码生成器导入成功")
        print("  启动方式: python registration_code_generator.py")
        return True
    except Exception as e:
        print(f"  ✗ 注册码生成器测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试完整的客户端系统...")
    
    success1 = test_complete_workflow()
    success2 = test_registration_code_generator()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
        print("\n✅ 完整的客户端系统已实现：")
        print("  • 模型上传到相对路径并自动加密")
        print("  • 客户端自动生成和分发")
        print("  • 客户端包含简化功能：")
        print("    - 自动机器码检测")
        print("    - 注册码输入激活")
        print("    - 模型运行管理")
        print("    - 运行日志查看")
        print("    - 加密模型自动解密运行")
        
        print("\n🛠️ 使用流程：")
        print("  1. 主程序导入FMW模型（自动加密）")
        print("  2. 生成客户端分发包")
        print("  3. 使用注册码生成器为客户生成注册码")
        print("  4. 客户端用户输入注册码激活")
        print("  5. 客户端运行加密模型（自动解密）")
        
        print("\n🚀 启动方式：")
        print("  主程序: python main.py")
        print("  注册码生成器: python registration_code_generator.py")
        print("  客户端: python client_template.py")
    else:
        print("\n❌ 部分测试失败，请检查相关功能")
    
    sys.exit(0 if (success1 and success2) else 1)
