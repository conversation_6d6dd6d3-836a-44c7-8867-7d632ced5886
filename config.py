"""
FME模型管理工具配置文件
"""
import os
import json
from pathlib import Path

# 应用基础配置
APP_NAME = "FME模型管理工具"
APP_VERSION = "1.0.0"
APP_AUTHOR = "FME Tools"

# 默认路径配置
DEFAULT_FME_PATH = r"C:\Program Files\FME\fme.exe"
DEFAULT_WORKSPACE_DIR = os.path.join(os.path.expanduser("~"), "FME_Workspace")
DEFAULT_MODELS_DIR = os.path.join(DEFAULT_WORKSPACE_DIR, "models")
DEFAULT_OUTPUT_DIR = os.path.join(DEFAULT_WORKSPACE_DIR, "output")
DEFAULT_TEMP_DIR = os.path.join(DEFAULT_WORKSPACE_DIR, "temp")
DEFAULT_CLIENTS_DIR = os.path.join(DEFAULT_WORKSPACE_DIR, "clients")

# 加密配置
ENCRYPTION_KEY_FILE = "encryption.key"
ENCRYPTED_EXTENSION = ".fmw_encrypted"

# 分发客户端配置
CLIENT_CONFIG_FILE = "client_config.json"
LICENSE_FILE = "license.json"

# 支持的文件类型
SUPPORTED_FMW_EXTENSIONS = [".fmw"]
SUPPORTED_ARCHIVE_EXTENSIONS = [".zip", ".rar", ".7z"]

# UI配置
WINDOW_SIZE = "1200x800"
THEME = "cosmo"  # ttkbootstrap主题

class Config:
    """配置管理类"""
    
    def __init__(self):
        self.config_file = "app_config.json"
        self.config = self.load_config()
        
    def load_config(self):
        """加载配置文件"""
        default_config = {
            "app_name": APP_NAME,
            "app_version": APP_VERSION,
            "app_author": APP_AUTHOR,
            "fme_path": DEFAULT_FME_PATH,
            "workspace_dir": DEFAULT_WORKSPACE_DIR,
            "models_dir": DEFAULT_MODELS_DIR,
            "output_dir": DEFAULT_OUTPUT_DIR,
            "temp_dir": DEFAULT_TEMP_DIR,
            "clients_dir": DEFAULT_CLIENTS_DIR,
            "theme": THEME,
            "window_size": WINDOW_SIZE,
            "auto_backup": True,
            "max_recent_files": 10,
            "recent_files": []
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    default_config.update(loaded_config)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                
        return default_config
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get(self, key, default=None):
        """获取配置项"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """设置配置项"""
        self.config[key] = value
        self.save_config()
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        dirs = [
            self.get("workspace_dir"),
            self.get("models_dir"),
            self.get("output_dir"),
            self.get("temp_dir"),
            self.get("clients_dir")
        ]
        
        for dir_path in dirs:
            if dir_path and not os.path.exists(dir_path):
                try:
                    os.makedirs(dir_path, exist_ok=True)
                    print(f"创建目录: {dir_path}")
                except Exception as e:
                    print(f"创建目录失败 {dir_path}: {e}")

# 全局配置实例
config = Config()
