# FME模型管理工具界面说明

## 🖥️ 主界面展示

### 整体布局
```
┌─────────────────────────────────────────────────────────────────┐
│ 文件(F)  模型(M)  运行(R)  分发(D)  帮助(H)                      │ 菜单栏
├─────────────────────────────────────────────────────────────────┤
│ [导入] [运行] [加密] [客户端] │ [刷新] [设置]                    │ 工具栏
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐  │  ┌─────────────────────────────────────┐ │
│  │   模型列表      │  │  │        模型详情                    │ │
│  │                 │  │  │                                     │ │
│  │ 搜索: [_______] │  │  │  ┌─────┬─────┬─────┐                │ │
│  │ 分类: [全部 ▼] │  │  │  │信息 │参数 │日志 │                │ │
│  │                 │  │  │  └─────┴─────┴─────┘                │ │
│  │ ┌─────────────┐ │  │  │                                     │ │
│  │ │模型名│分类│..│ │  │  │  [模型信息显示区域]                │ │
│  │ ├─────────────┤ │  │  │                                     │ │
│  │ │模型A │转换│..│ │  │  │  或                                │ │
│  │ │模型B │分析│..│ │  │  │                                     │ │
│  │ │模型C │质检│..│ │  │  │  [参数设置表单]                    │ │
│  │ └─────────────┘ │  │  │                                     │ │
│  └─────────────────┘  │  │  或                                │ │
│                       │  │                                     │ │
│                       │  │  [运行日志显示]                    │ │
│                       │  └─────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 状态: 就绪                                    [进度条]          │ 状态栏
└─────────────────────────────────────────────────────────────────┘
```

## 🎨 界面特色

### 1. 现代化设计
- **Material Design** 风格的按钮和控件
- **扁平化** 设计语言
- **一致的颜色** 主题和图标
- **圆角边框** 和阴影效果

### 2. 颜色方案
- **主色调**: 蓝色 (#007bff)
- **成功色**: 绿色 (#28a745) - 运行按钮
- **警告色**: 橙色 (#ffc107) - 加密按钮
- **危险色**: 红色 (#dc3545) - 删除操作
- **次要色**: 灰色 (#6c757d) - 辅助按钮

### 3. 交互效果
- **悬停效果** - 按钮悬停时颜色变化
- **点击反馈** - 按钮点击时的视觉反馈
- **加载动画** - 进度条和加载指示器
- **平滑过渡** - 界面切换的动画效果

## 📋 功能界面详解

### 模型列表界面
```
┌─────────────────────────────────────┐
│ 搜索: [输入关键词搜索模型...    ] │
│ 分类: [全部 ▼] [默认] [数据转换]   │
├─────────────────────────────────────┤
│ 模型名称    │分类│大小│时间│加密│次数│
├─────────────────────────────────────┤
│ CAD转换模型 │转换│2MB │今天│否  │ 5  │
│ 数据质检    │质检│1MB │昨天│是  │ 3  │
│ 坐标转换    │转换│3MB │3天 │否  │ 8  │
└─────────────────────────────────────┘
```

### 参数设置界面
```
┌─────────────────────────────────────────┐
│ 参数设置                                │
├─────────────────────────────────────────┤
│ 输入文件: [选择文件路径...    ] [浏览]  │
│ 输出目录: [选择输出目录...    ] [浏览]  │
│ 坐标系统: [EPSG:4326        ▼]         │
│ 精度设置: [高精度 ○] [标准 ●] [快速 ○] │
│ 启用日志: [✓] 详细日志                  │
│                                         │
│        [运行模型] [重置参数]            │
└─────────────────────────────────────────┘
```

### 运行日志界面
```
┌─────────────────────────────────────────┐
│ 运行日志                    [清除日志] │
├─────────────────────────────────────────┤
│ [2024-08-12 14:30:15] 开始运行模型...  │
│ [2024-08-12 14:30:16] 读取输入文件...  │
│ [2024-08-12 14:30:18] 处理数据中...    │
│ [2024-08-12 14:30:25] 写入输出文件...  │
│ [2024-08-12 14:30:28] 模型运行完成     │
│                                         │
│ ████████████████████████████████ 100%   │
└─────────────────────────────────────────┘
```

## 🔧 对话框界面

### 模型导入对话框
```
┌─────────────────────────────────────────┐
│ 导入模型                           [×]  │
├─────────────────────────────────────────┤
│ 文件路径:                               │
│ C:\Models\data_converter.fmw            │
│                                         │
│ 模型名称: [数据转换模型          ]      │
│                                         │
│ 模型描述:                               │
│ ┌─────────────────────────────────────┐ │
│ │这是一个用于数据格式转换的FMW模型    │ │
│ │支持多种GIS数据格式之间的转换        │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ 模型分类: [数据转换 ▼]                  │
│                                         │
│              [确定] [取消]              │
└─────────────────────────────────────────┘
```

### 客户端创建对话框
```
┌─────────────────────────────────────────┐
│ 创建分发客户端                     [×]  │
├─────────────────────────────────────────┤
│ ┌─────┬─────┬─────┐                     │
│ │基本 │模型 │许可 │                     │
│ └─────┴─────┴─────┘                     │
│                                         │
│ 客户端名称: [GIS数据处理工具     ]      │
│                                         │
│ 客户端描述:                             │
│ ┌─────────────────────────────────────┐ │
│ │专业的GIS数据处理客户端              │ │
│ │包含数据转换、质检等功能             │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ 许可证设置:                             │
│ [✓] 过期时间: [30] 天                   │
│ [✓] 使用次数: [100] 次                  │
│ [ ] 机器码限制                          │
│                                         │
│              [创建] [取消]              │
└─────────────────────────────────────────┘
```

## 🎯 界面操作流程

### 1. 导入模型
1. 点击工具栏"导入模型"按钮
2. 选择FMW文件
3. 填写模型信息
4. 确认导入

### 2. 运行模型
1. 在模型列表中选择模型
2. 切换到"参数设置"选项卡
3. 配置运行参数
4. 点击"运行模型"按钮
5. 在"运行日志"选项卡查看进度

### 3. 创建客户端
1. 点击工具栏"创建客户端"按钮
2. 填写客户端基本信息
3. 选择要包含的模型
4. 配置许可证限制
5. 生成分发包

## 🌈 主题展示

程序支持多种主题，用户可以在设置中切换：

- **cosmo** - 现代蓝色主题（默认）
- **flatly** - 扁平绿色主题
- **journal** - 经典报纸风格
- **litera** - 简洁白色主题
- **lumen** - 明亮主题
- **minty** - 薄荷绿主题
- **pulse** - 紫色主题
- **sandstone** - 沙石色主题
- **united** - 橙色主题
- **yeti** - 雪白主题

每个主题都有完整的颜色方案和视觉风格，确保界面的一致性和美观性。
