"""
测试自动加密导入功能
"""
import os
import sys
import tempfile
import shutil
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def create_test_fmw():
    """创建测试FMW文件"""
    fmw_content = '''#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#!   TITLE="测试加密导入模型"
#!   DESCRIPTION="用于测试自动加密导入功能的FMW文件"
#!   CATEGORY="测试"
#! >
#! 
#! <USER_PARAMETERS>
#!   <USER_PARAMETER
#!     GUI_LINE="INPUT_FILE|文件|文件|"
#!     DEFAULT_VALUE=""
#!     IS_STAND_ALONE="true"
#!     PARM_NAME="INPUT_FILE"
#!     PROMPT="输入文件:"
#!     TYPE="FILENAME_MUSTEXIST"
#!   />
#!   <USER_PARAMETER
#!     GUI_LINE="OUTPUT_DIR|文件夹|文件夹|"
#!     DEFAULT_VALUE=""
#!     IS_STAND_ALONE="true"
#!     PARM_NAME="OUTPUT_DIR"
#!     PROMPT="输出目录:"
#!     TYPE="DIRNAME"
#!   />
#! </USER_PARAMETERS>
#! 
#! </WORKSPACE>'''
    
    return fmw_content

def test_auto_encryption_import():
    """测试自动加密导入功能"""
    print("=" * 60)
    print("测试自动加密导入功能")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 设置临时配置
        from config import config
        original_models_dir = config.get("models_dir")
        config.set("models_dir", temp_dir)
        
        # 创建测试FMW文件
        test_fmw_content = create_test_fmw()
        test_fmw_path = os.path.join(temp_dir, "test_encryption.fmw")
        
        with open(test_fmw_path, 'w', encoding='utf-8') as f:
            f.write(test_fmw_content)
        
        print(f"✓ 创建测试FMW文件: {test_fmw_path}")
        
        # 测试自动加密导入
        from model_manager import ModelManager
        
        manager = ModelManager()
        
        print("\n1. 测试自动加密导入...")
        success, message, model_id = manager.import_model(
            test_fmw_path,
            "测试加密模型",
            "这是一个测试自动加密导入的模型",
            "测试分类",
            auto_encrypt=True  # 启用自动加密
        )
        
        if success:
            print(f"✓ 自动加密导入成功: {model_id}")
            
            # 验证模型信息
            model_info = manager.get_model_info(model_id)
            if model_info:
                print(f"  模型名称: {model_info['name']}")
                print(f"  加密状态: {'已加密' if model_info['is_encrypted'] else '未加密'}")
                print(f"  原始文件: {model_info.get('original_file_path', '无')}")
                print(f"  加密文件: {model_info.get('encrypted_file_path', '无')}")
                
                # 验证加密文件存在
                if model_info['is_encrypted']:
                    encrypted_path = model_info['encrypted_file_path']
                    if os.path.exists(encrypted_path):
                        print(f"✓ 加密文件存在: {encrypted_path}")
                    else:
                        print(f"✗ 加密文件不存在: {encrypted_path}")
                
                # 验证原始文件不存在（应该只保存加密文件）
                original_path = model_info.get('original_file_path', '')
                if original_path and not original_path.endswith('.encrypted'):
                    if not os.path.exists(original_path):
                        print("✓ 原始文件已删除，只保留加密文件")
                    else:
                        print("⚠️ 原始文件仍然存在")
            
        else:
            print(f"✗ 自动加密导入失败: {message}")
            return False
        
        print("\n2. 测试运行时解密...")
        
        # 测试获取运行时路径
        runtime_path = manager.get_runtime_fmw_path(model_id)
        if runtime_path:
            print(f"✓ 获取运行时路径成功: {runtime_path}")
            
            # 验证解密文件存在且可读
            if os.path.exists(runtime_path):
                print("✓ 运行时解密文件存在")
                
                # 验证解密文件内容
                with open(runtime_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "测试加密导入模型" in content:
                        print("✓ 解密文件内容正确")
                    else:
                        print("✗ 解密文件内容异常")
            else:
                print(f"✗ 运行时解密文件不存在: {runtime_path}")
        else:
            print("✗ 获取运行时路径失败")
        
        print("\n3. 测试非加密导入...")
        
        # 创建另一个测试文件
        test_fmw_path2 = os.path.join(temp_dir, "test_no_encryption.fmw")
        with open(test_fmw_path2, 'w', encoding='utf-8') as f:
            f.write(test_fmw_content.replace("测试加密导入模型", "测试非加密导入模型"))
        
        success2, message2, model_id2 = manager.import_model(
            test_fmw_path2,
            "测试非加密模型",
            "这是一个测试非加密导入的模型",
            "测试分类",
            auto_encrypt=False  # 不加密
        )
        
        if success2:
            print(f"✓ 非加密导入成功: {model_id2}")
            
            model_info2 = manager.get_model_info(model_id2)
            if model_info2:
                print(f"  加密状态: {'已加密' if model_info2['is_encrypted'] else '未加密'}")
                
                if not model_info2['is_encrypted']:
                    print("✓ 非加密导入正确")
                else:
                    print("✗ 非加密导入异常，模型被意外加密")
        else:
            print(f"✗ 非加密导入失败: {message2}")
        
        print("\n4. 测试客户端分发...")
        
        # 测试客户端生成
        from client_generator import ClientGenerator
        
        # 创建临时客户端目录
        clients_temp_dir = os.path.join(temp_dir, "clients")
        os.makedirs(clients_temp_dir, exist_ok=True)
        
        original_clients_dir = config.get("clients_dir")
        config.set("clients_dir", clients_temp_dir)
        
        try:
            generator = ClientGenerator()
            
            # 生成包含加密模型的客户端
            success, message, zip_path = generator.generate_client(
                "测试加密客户端",
                [model_id],  # 包含加密模型
                {"expire_days": 30}
            )
            
            if success:
                print(f"✓ 客户端生成成功: {zip_path}")
                
                # 验证客户端包含加密文件
                import zipfile
                with zipfile.ZipFile(zip_path, 'r') as zf:
                    file_list = zf.namelist()
                    encrypted_files = [f for f in file_list if f.endswith('.encrypted')]
                    if encrypted_files:
                        print(f"✓ 客户端包含加密文件: {encrypted_files}")
                    else:
                        print("⚠️ 客户端未包含加密文件")
            else:
                print(f"✗ 客户端生成失败: {message}")
        
        finally:
            config.set("clients_dir", original_clients_dir)
        
        # 恢复原始配置
        config.set("models_dir", original_models_dir)
        
        print("\n" + "=" * 60)
        print("自动加密导入功能测试完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir, ignore_errors=True)

def test_ui_integration():
    """测试UI集成"""
    print("\n5. 测试UI集成...")
    
    try:
        import tkinter as tk
        from dialogs import ModelImportDialog
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        print("✓ UI组件导入成功")
        print("  导入对话框已包含自动加密选项")
        print("  默认启用自动加密保护")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ UI集成测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试自动加密导入功能...")
    
    success1 = test_auto_encryption_import()
    success2 = test_ui_integration()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
        print("\n✅ 自动加密导入功能已完整实现：")
        print("  • 导入时自动加密FMW文件")
        print("  • 运行时自动解密到临时文件")
        print("  • 分发时只包含加密文件")
        print("  • 原始FMW文件得到完全保护")
        print("  • 用户使用完全透明")
        
        print("\n🚀 使用方式：")
        print("  1. 启动主程序: python main.py")
        print("  2. 导入模型时勾选'自动加密模型文件'")
        print("  3. 程序自动加密保存，运行时自动解密")
        print("  4. 分发客户端时只包含加密文件")
    else:
        print("\n❌ 部分测试失败，请检查相关功能")
    
    sys.exit(0 if (success1 and success2) else 1)
