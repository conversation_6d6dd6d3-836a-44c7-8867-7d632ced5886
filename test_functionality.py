"""
FME模型管理工具功能测试脚本
"""
import os
import sys
import tempfile
import shutil
from datetime import datetime, timedelta

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_config():
    """测试配置模块"""
    print("测试配置模块...")
    try:
        from config import config
        
        # 测试基本配置
        assert config.get("app_name") is not None
        assert config.get("workspace_dir") is not None
        
        # 测试设置和获取
        test_key = "test_setting"
        test_value = "test_value"
        config.set(test_key, test_value)
        assert config.get(test_key) == test_value
        
        # 测试目录创建
        config.ensure_directories()
        
        print("✓ 配置模块测试通过")
        return True
    except Exception as e:
        print(f"✗ 配置模块测试失败: {e}")
        return False

def test_encryption():
    """测试加密模块"""
    print("测试加密模块...")
    try:
        from encryption import FMWEncryption, LicenseManager
        
        # 测试文件加密
        encryption = FMWEncryption()
        
        # 创建测试文件
        test_content = "这是一个测试FMW文件内容"
        with tempfile.NamedTemporaryFile(mode='w', suffix='.fmw', delete=False) as f:
            f.write(test_content)
            test_file = f.name
        
        encrypted_file = None
        decrypted_file = None

        try:
            # 测试加密
            encrypted_file = encryption.encrypt_file(test_file)
            assert os.path.exists(encrypted_file)

            # 测试解密
            decrypted_file = encryption.decrypt_file(encrypted_file)
            assert os.path.exists(decrypted_file)

            # 验证内容
            with open(decrypted_file, 'r') as f:
                decrypted_content = f.read()
            assert decrypted_content == test_content

        finally:
            # 清理文件
            for file_path in [test_file, encrypted_file, decrypted_file]:
                if file_path and os.path.exists(file_path):
                    try:
                        os.unlink(file_path)
                    except:
                        pass
        
        # 测试许可证管理
        license_manager = LicenseManager()
        
        # 生成测试许可证
        expire_date = datetime.now() + timedelta(days=30)
        license_data = license_manager.generate_license(
            expire_date=expire_date,
            max_uses=100
        )
        
        assert license_data is not None
        assert license_data.get("expire_date") is not None
        assert license_data.get("max_uses") == 100
        
        # 验证许可证
        valid, message = license_manager.validate_license(license_data)
        assert valid, f"许可证验证失败: {message}"
        
        print("✓ 加密模块测试通过")
        return True
    except Exception as e:
        print(f"✗ 加密模块测试失败: {e}")
        return False

def test_model_manager():
    """测试模型管理模块"""
    print("测试模型管理模块...")
    try:
        from model_manager import ModelManager
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        try:
            # 修改配置使用临时目录
            from config import config
            original_models_dir = config.get("models_dir")
            config.set("models_dir", temp_dir)
            
            manager = ModelManager()
            
            # 创建测试FMW文件
            test_fmw_content = '''#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE>
#! </WORKSPACE>'''
            
            test_fmw_path = os.path.join(temp_dir, "test_model.fmw")
            with open(test_fmw_path, 'w', encoding='utf-8') as f:
                f.write(test_fmw_content)
            
            # 测试导入模型
            success, message, model_id = manager.import_model(
                test_fmw_path, 
                "测试模型", 
                "这是一个测试模型", 
                "测试分类"
            )
            
            assert success, f"导入模型失败: {message}"
            assert model_id is not None
            
            # 测试获取模型信息
            model_info = manager.get_model_info(model_id)
            assert model_info is not None
            assert model_info["name"] == "测试模型"
            
            # 测试搜索模型
            search_results = manager.search_models("测试")
            assert model_id in search_results
            
            # 测试加密模型
            success, message = manager.encrypt_model(model_id)
            assert success, f"加密模型失败: {message}"
            
            # 测试解密模型
            success, message, decrypted_path = manager.decrypt_model(model_id)
            assert success, f"解密模型失败: {message}"
            assert os.path.exists(decrypted_path)
            
            # 恢复原始配置
            config.set("models_dir", original_models_dir)
            
        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)
        
        print("✓ 模型管理模块测试通过")
        return True
    except Exception as e:
        print(f"✗ 模型管理模块测试失败: {e}")
        return False

def test_fmw_runner():
    """测试FMW运行模块"""
    print("测试FMW运行模块...")
    try:
        from fmw_runner import FMWRunner, TaskManager
        
        # 测试任务管理器
        task_manager = TaskManager()
        
        # 创建测试任务
        task_id = task_manager.create_task(
            "test.fmw", 
            {"param1": "value1"}, 
            "/output"
        )
        
        assert task_id is not None
        
        # 测试获取任务
        task = task_manager.get_task(task_id)
        assert task is not None
        assert task["fmw_path"] == "test.fmw"
        
        # 测试更新任务状态
        task_manager.update_task_status(task_id, "running")
        task = task_manager.get_task(task_id)
        assert task["status"] == "running"
        
        # 测试FMW运行器
        runner = FMWRunner("dummy_fme_path")
        
        # 测试命令构建
        cmd = runner.build_command("test.fmw", {"param1": "value1"})
        assert "dummy_fme_path" in cmd
        assert "test.fmw" in cmd
        assert "--param1" in cmd
        assert "value1" in cmd
        
        print("✓ FMW运行模块测试通过")
        return True
    except Exception as e:
        print(f"✗ FMW运行模块测试失败: {e}")
        return False

def test_client_generator():
    """测试客户端生成模块"""
    print("测试客户端生成模块...")
    try:
        from client_generator import ClientGenerator
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        try:
            # 修改配置使用临时目录
            from config import config
            original_clients_dir = config.get("clients_dir")
            config.set("clients_dir", temp_dir)
            
            generator = ClientGenerator()
            
            # 测试客户端模板创建
            template = generator.create_client_template()
            assert template is not None
            assert len(template) > 0
            
            # 测试获取客户端列表
            clients = generator.get_client_list()
            assert isinstance(clients, list)
            
            # 恢复原始配置
            config.set("clients_dir", original_clients_dir)
            
        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)
        
        print("✓ 客户端生成模块测试通过")
        return True
    except Exception as e:
        print(f"✗ 客户端生成模块测试失败: {e}")
        return False

def test_parse_fmw():
    """测试FMW解析模块"""
    print("测试FMW解析模块...")
    try:
        from parse_fmw import parse_fmw_parameters
        
        # 检查测试文件是否存在
        test_fmw = "fmw参数解析.fmw"
        if os.path.exists(test_fmw):
            # 测试解析FMW参数
            result = parse_fmw_parameters(test_fmw)
            assert result is not None
            assert "parameters" in result
            print(f"  解析到 {len(result.get('parameters', []))} 个参数")
        else:
            print("  跳过FMW解析测试（测试文件不存在）")
        
        print("✓ FMW解析模块测试通过")
        return True
    except Exception as e:
        print(f"✗ FMW解析模块测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("=" * 50)
    print("FME模型管理工具功能测试")
    print("=" * 50)
    
    tests = [
        test_config,
        test_encryption,
        test_model_manager,
        test_fmw_runner,
        test_client_generator,
        test_parse_fmw
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关模块")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
