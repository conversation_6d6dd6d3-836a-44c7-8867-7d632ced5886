"""
分发客户端生成模块
"""
import os
import json
import shutil
import zipfile
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from config import config
from encryption import LicenseManager
from model_manager import ModelManager

class ClientGenerator:
    """分发客户端生成器"""
    
    def __init__(self):
        self.clients_dir = config.get("clients_dir")
        self.model_manager = ModelManager()
        self.license_manager = LicenseManager()
        
        # 确保客户端目录存在
        os.makedirs(self.clients_dir, exist_ok=True)
    
    def create_client_template(self):
        """创建客户端模板"""
        # 读取客户端模板文件
        template_file = "client_template.py"
        if os.path.exists(template_file):
            with open(template_file, 'r', encoding='utf-8') as f:
                return f.read()
        else:
            # 如果模板文件不存在，返回简化版本
            return '''"""
FME模型运行客户端
"""
import os
import sys
import json
import tkinter as tk
from tkinter import ttk, messagebox

class FMEClient:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("FME模型运行客户端")
        self.root.geometry("600x400")

        ttk.Label(self.root, text="FME模型运行客户端",
                 font=("", 16, "bold")).pack(pady=20)
        ttk.Label(self.root, text="客户端模板文件缺失，请联系管理员").pack(pady=10)

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    client = FMEClient()
    client.run()
'''
    
    def generate_client(self, client_name, selected_models, license_config):
        """生成分发客户端"""
        try:
            # 创建客户端目录
            client_id = f"client_{int(datetime.now().timestamp())}"
            client_dir = os.path.join(self.clients_dir, client_id)
            os.makedirs(client_dir, exist_ok=True)
            
            # 创建模型目录
            models_dir = os.path.join(client_dir, "models")
            os.makedirs(models_dir, exist_ok=True)
            
            # 复制选中的模型
            copied_models = []
            for model_id in selected_models:
                model_info = self.model_manager.get_model_info(model_id)
                if model_info:
                    # 如果模型已加密，使用加密文件
                    if model_info["is_encrypted"] and os.path.exists(model_info["encrypted_file_path"]):
                        source_file = model_info["encrypted_file_path"]
                        dest_file = os.path.join(models_dir, model_info["file_name"] + ".encrypted")
                    else:
                        source_file = model_info["file_path"]
                        dest_file = os.path.join(models_dir, model_info["file_name"])
                    
                    shutil.copy2(source_file, dest_file)
                    copied_models.append({
                        "id": model_id,
                        "name": model_info["name"],
                        "file_name": os.path.basename(dest_file),
                        "is_encrypted": model_info["is_encrypted"]
                    })
            
            # 生成许可证
            expire_date = None
            if license_config.get("expire_days"):
                expire_date = datetime.now() + timedelta(days=license_config["expire_days"])
            
            license_data = self.license_manager.generate_license(
                expire_date=expire_date,
                max_uses=license_config.get("max_uses"),
                allowed_machines=license_config.get("allowed_machines")
            )
            
            # 保存许可证
            license_file = os.path.join(client_dir, "license.json")
            self.license_manager.save_license(license_data, license_file)
            
            # 创建客户端配置
            client_config = {
                "client_name": client_name,
                "client_id": client_id,
                "created_at": datetime.now().isoformat(),
                "models": copied_models,
                "license": license_config,
                "fme_path": config.get("fme_path")
            }
            
            config_file = os.path.join(client_dir, "client_config.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(client_config, f, ensure_ascii=False, indent=2)
            
            # 创建客户端主程序
            client_script = os.path.join(client_dir, "fme_client.py")
            with open(client_script, 'w', encoding='utf-8') as f:
                f.write(self.create_client_template())
            
            # 创建启动脚本
            start_script = os.path.join(client_dir, "start.bat")
            with open(start_script, 'w', encoding='utf-8') as f:
                f.write(f'@echo off\ncd /d "%~dp0"\npython fme_client.py\npause')
            
            # 创建README文件
            readme_file = os.path.join(client_dir, "README.txt")
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(f"""FME模型运行客户端
客户端名称: {client_name}
创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
包含模型: {len(copied_models)}个

使用说明:
1. 确保已安装Python 3.7+
2. 双击start.bat启动客户端
3. 或者在命令行中运行: python fme_client.py

许可证信息:
- 过期时间: {expire_date.strftime('%Y-%m-%d') if expire_date else '无限制'}
- 使用次数限制: {license_config.get('max_uses', '无限制')}
- 机器码限制: {'是' if license_config.get('allowed_machines') else '否'}
""")
            
            # 打包客户端
            zip_file = os.path.join(self.clients_dir, f"{client_name}_{client_id}.zip")
            with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(client_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_name = os.path.relpath(file_path, client_dir)
                        zipf.write(file_path, arc_name)
            
            return True, f"客户端生成成功: {zip_file}", zip_file
            
        except Exception as e:
            return False, f"客户端生成失败: {e}", None
    
    def get_client_list(self):
        """获取已生成的客户端列表"""
        clients = []
        if os.path.exists(self.clients_dir):
            for item in os.listdir(self.clients_dir):
                if item.endswith('.zip'):
                    clients.append(item)
        return clients
