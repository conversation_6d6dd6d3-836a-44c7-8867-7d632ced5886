"""
FME模型管理工具主程序
"""
import sys
import os
import traceback
from tkinter import messagebox

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from main_window import MainWindow
    from dialogs import *
    from config import config
    import logging
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('fme_tool.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    
    def main():
        """主函数"""
        try:
            logger.info("启动FME模型管理工具")
            
            # 检查必要的依赖
            check_dependencies()
            
            # 确保必要目录存在
            config.ensure_directories()
            
            # 创建并运行主窗口
            app = MainWindow()
            app.run()
            
            logger.info("FME模型管理工具正常退出")
            
        except Exception as e:
            logger.error(f"程序运行出错: {e}")
            logger.error(traceback.format_exc())
            
            # 显示错误对话框
            try:
                messagebox.showerror("错误", f"程序运行出错:\n{e}\n\n详细信息请查看日志文件 fme_tool.log")
            except:
                print(f"程序运行出错: {e}")
            
            sys.exit(1)
    
    def check_dependencies():
        """检查依赖包"""
        required_packages = [
            'tkinter',
            'ttkbootstrap',
            'cryptography',
            'psutil',
            'Pillow'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                if package == 'tkinter':
                    import tkinter
                elif package == 'ttkbootstrap':
                    import ttkbootstrap
                elif package == 'cryptography':
                    import cryptography
                elif package == 'psutil':
                    import psutil
                elif package == 'Pillow':
                    import PIL
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            error_msg = f"缺少必要的依赖包: {', '.join(missing_packages)}\n\n"
            error_msg += "请运行以下命令安装:\n"
            error_msg += f"pip install {' '.join(missing_packages)}"
            
            logger.error(error_msg)
            
            try:
                messagebox.showerror("依赖错误", error_msg)
            except:
                print(error_msg)
            
            sys.exit(1)
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    error_msg = f"导入模块失败: {e}\n\n请确保所有必要的文件都在同一目录下，并且已安装所需的依赖包。"
    
    try:
        messagebox.showerror("导入错误", error_msg)
    except:
        print(error_msg)
    
    sys.exit(1)
    
except Exception as e:
    error_msg = f"程序初始化失败: {e}\n\n{traceback.format_exc()}"
    
    try:
        messagebox.showerror("初始化错误", error_msg)
    except:
        print(error_msg)
    
    sys.exit(1)
