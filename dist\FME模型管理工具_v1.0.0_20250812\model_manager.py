"""
FMW模型管理模块
"""
import os
import json
import shutil
import zipfile
import hashlib
from datetime import datetime
from pathlib import Path
from config import config
from parse_fmw import parse_fmw_parameters
from encryption import FMWEncryption

class ModelManager:
    """FMW模型管理器"""
    
    def __init__(self):
        self.models_dir = config.get("models_dir")
        self.models_index_file = os.path.join(self.models_dir, "models_index.json")
        self.models_index = self.load_models_index()
        self.encryption = FMWEncryption()
        
        # 确保模型目录存在
        os.makedirs(self.models_dir, exist_ok=True)
    
    def load_models_index(self):
        """加载模型索引"""
        if os.path.exists(self.models_index_file):
            try:
                with open(self.models_index_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载模型索引失败: {e}")
        
        return {"models": {}, "last_updated": datetime.now().isoformat()}
    
    def save_models_index(self):
        """保存模型索引"""
        try:
            self.models_index["last_updated"] = datetime.now().isoformat()
            with open(self.models_index_file, 'w', encoding='utf-8') as f:
                json.dump(self.models_index, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存模型索引失败: {e}")
    
    def calculate_file_hash(self, file_path):
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            print(f"计算文件哈希失败: {e}")
            return None
    
    def import_model(self, fmw_path, model_name=None, description="", category="默认"):
        """导入FMW模型"""
        if not os.path.exists(fmw_path):
            raise FileNotFoundError(f"FMW文件不存在: {fmw_path}")
        
        # 生成模型ID
        file_hash = self.calculate_file_hash(fmw_path)
        if not file_hash:
            raise Exception("无法计算文件哈希值")
        
        model_id = file_hash[:16]  # 使用哈希值前16位作为ID
        
        # 检查模型是否已存在
        if model_id in self.models_index["models"]:
            return False, f"模型已存在: {model_id}", model_id
        
        # 创建模型目录
        model_dir = os.path.join(self.models_dir, model_id)
        os.makedirs(model_dir, exist_ok=True)
        
        # 复制FMW文件
        model_file_name = model_name or os.path.basename(fmw_path)
        if not model_file_name.endswith('.fmw'):
            model_file_name += '.fmw'
        
        model_file_path = os.path.join(model_dir, model_file_name)
        shutil.copy2(fmw_path, model_file_path)
        
        # 解析FMW参数
        try:
            fmw_params = parse_fmw_parameters(model_file_path)
            parameters = fmw_params.get('parameters', []) if fmw_params else []
        except Exception as e:
            print(f"解析FMW参数失败: {e}")
            parameters = []
        
        # 创建模型信息
        model_info = {
            "id": model_id,
            "name": model_name or os.path.splitext(os.path.basename(fmw_path))[0],
            "description": description,
            "category": category,
            "file_name": model_file_name,
            "file_path": model_file_path,
            "file_hash": file_hash,
            "file_size": os.path.getsize(model_file_path),
            "parameters": parameters,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "is_encrypted": False,
            "encrypted_file_path": None,
            "usage_count": 0,
            "last_used": None
        }
        
        # 添加到索引
        self.models_index["models"][model_id] = model_info
        self.save_models_index()
        
        return True, f"模型导入成功: {model_info['name']}", model_id
    
    def encrypt_model(self, model_id):
        """加密模型"""
        if model_id not in self.models_index["models"]:
            return False, "模型不存在"
        
        model_info = self.models_index["models"][model_id]
        
        if model_info["is_encrypted"]:
            return False, "模型已加密"
        
        try:
            # 加密文件
            encrypted_path = model_info["file_path"] + ".encrypted"
            self.encryption.encrypt_file(model_info["file_path"], encrypted_path)
            
            # 更新模型信息
            model_info["is_encrypted"] = True
            model_info["encrypted_file_path"] = encrypted_path
            model_info["updated_at"] = datetime.now().isoformat()
            
            self.save_models_index()
            return True, "模型加密成功"
            
        except Exception as e:
            return False, f"模型加密失败: {e}"
    
    def decrypt_model(self, model_id, output_path=None):
        """解密模型"""
        if model_id not in self.models_index["models"]:
            return False, "模型不存在", None
        
        model_info = self.models_index["models"][model_id]
        
        if not model_info["is_encrypted"]:
            return False, "模型未加密", model_info["file_path"]
        
        try:
            if output_path is None:
                output_path = os.path.join(config.get("temp_dir"), f"{model_id}_decrypted.fmw")
                os.makedirs(config.get("temp_dir"), exist_ok=True)
            
            # 解密文件
            self.encryption.decrypt_file(model_info["encrypted_file_path"], output_path)
            
            return True, "模型解密成功", output_path
            
        except Exception as e:
            return False, f"模型解密失败: {e}", None
    
    def delete_model(self, model_id):
        """删除模型"""
        if model_id not in self.models_index["models"]:
            return False, "模型不存在"
        
        model_info = self.models_index["models"][model_id]
        
        try:
            # 删除模型目录
            model_dir = os.path.dirname(model_info["file_path"])
            if os.path.exists(model_dir):
                shutil.rmtree(model_dir)
            
            # 从索引中删除
            del self.models_index["models"][model_id]
            self.save_models_index()
            
            return True, "模型删除成功"
            
        except Exception as e:
            return False, f"模型删除失败: {e}"
    
    def get_model_info(self, model_id):
        """获取模型信息"""
        return self.models_index["models"].get(model_id)
    
    def get_all_models(self):
        """获取所有模型"""
        return self.models_index["models"]
    
    def search_models(self, keyword="", category=""):
        """搜索模型"""
        models = self.models_index["models"]
        results = {}
        
        for model_id, model_info in models.items():
            # 关键词搜索
            if keyword:
                if (keyword.lower() not in model_info["name"].lower() and 
                    keyword.lower() not in model_info["description"].lower()):
                    continue
            
            # 分类筛选
            if category and model_info["category"] != category:
                continue
            
            results[model_id] = model_info
        
        return results
    
    def update_model_usage(self, model_id):
        """更新模型使用统计"""
        if model_id in self.models_index["models"]:
            model_info = self.models_index["models"][model_id]
            model_info["usage_count"] = model_info.get("usage_count", 0) + 1
            model_info["last_used"] = datetime.now().isoformat()
            self.save_models_index()
    
    def export_model(self, model_id, export_path):
        """导出模型"""
        if model_id not in self.models_index["models"]:
            return False, "模型不存在"
        
        model_info = self.models_index["models"][model_id]
        
        try:
            # 创建导出包
            with zipfile.ZipFile(export_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 添加FMW文件
                zipf.write(model_info["file_path"], model_info["file_name"])
                
                # 添加加密文件（如果存在）
                if model_info["is_encrypted"] and os.path.exists(model_info["encrypted_file_path"]):
                    zipf.write(model_info["encrypted_file_path"], 
                              model_info["file_name"] + ".encrypted")
                
                # 添加模型信息
                model_info_json = json.dumps(model_info, ensure_ascii=False, indent=2)
                zipf.writestr("model_info.json", model_info_json)
            
            return True, f"模型导出成功: {export_path}"
            
        except Exception as e:
            return False, f"模型导出失败: {e}"
