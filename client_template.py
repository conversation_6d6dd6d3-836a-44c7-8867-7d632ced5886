"""
FME模型运行客户端模板
此文件将被复制到分发客户端中
"""
import os
import sys
import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import subprocess
from datetime import datetime
import platform
import hashlib
import base64
from pathlib import Path

class FMEClient:
    """FME客户端主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("FME模型运行客户端")
        self.root.geometry("900x700")
        
        # 加载配置和许可证
        self.config = self.load_config()
        self.license = self.load_license()
        
        # 验证许可证
        if not self.validate_license():
            messagebox.showerror("许可证错误", "许可证验证失败，程序将退出")
            sys.exit(1)
        
        # 设置界面
        self.setup_ui()
        
        # 加载可用模型
        self.load_models()
    
    def load_config(self):
        """加载客户端配置"""
        try:
            with open("client_config.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            messagebox.showerror("配置错误", f"加载配置文件失败: {e}")
            sys.exit(1)
    
    def load_license(self):
        """加载许可证"""
        try:
            with open("license.json", "rb") as f:
                data = base64.b64decode(f.read())
                
            # 提取密钥和加密数据
            key = data[:44]  # Fernet密钥长度为44字节
            encrypted_data = data[44:]
            
            from cryptography.fernet import Fernet
            fernet = Fernet(key)
            decrypted_data = fernet.decrypt(encrypted_data)
            
            return json.loads(decrypted_data.decode())
        except Exception as e:
            messagebox.showerror("许可证错误", f"加载许可证失败: {e}")
            sys.exit(1)
    
    def get_machine_id(self):
        """获取机器唯一标识"""
        machine_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
        return hashlib.md5(machine_info.encode()).hexdigest()
    
    def validate_license(self):
        """验证许可证"""
        if not self.license or not self.license.get("is_active"):
            return False
        
        # 检查过期时间
        if self.license.get("expire_date"):
            try:
                expire_date = datetime.fromisoformat(self.license["expire_date"])
                if datetime.now() > expire_date:
                    messagebox.showerror("许可证过期", "许可证已过期")
                    return False
            except:
                return False
        
        # 检查使用次数
        max_uses = self.license.get("max_uses")
        if max_uses and self.license.get("current_uses", 0) >= max_uses:
            messagebox.showerror("使用次数超限", "许可证使用次数已达上限")
            return False
        
        # 检查机器码
        allowed_machines = self.license.get("allowed_machines", [])
        if allowed_machines:
            machine_id = self.get_machine_id()
            if machine_id not in allowed_machines:
                messagebox.showerror("机器码不匹配", "此机器无权使用该客户端")
                return False
        
        return True
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="FME模型运行客户端", 
                               font=("", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 模型选择
        ttk.Label(main_frame, text="选择模型:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.model_var = tk.StringVar()
        self.model_combo = ttk.Combobox(main_frame, textvariable=self.model_var, 
                                       width=50, state="readonly")
        self.model_combo.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        self.model_combo.bind('<<ComboboxSelected>>', self.on_model_select)
        
        # 参数设置区域
        params_label = ttk.Label(main_frame, text="参数设置:")
        params_label.grid(row=2, column=0, sticky=(tk.W, tk.N), pady=5)
        
        # 参数框架（带滚动条）
        self.params_canvas = tk.Canvas(main_frame, height=200)
        params_scrollbar = ttk.Scrollbar(main_frame, orient="vertical", 
                                        command=self.params_canvas.yview)
        self.params_scrollable_frame = ttk.Frame(self.params_canvas)
        
        self.params_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.params_canvas.configure(scrollregion=self.params_canvas.bbox("all"))
        )
        
        self.params_canvas.create_window((0, 0), window=self.params_scrollable_frame, anchor="nw")
        self.params_canvas.configure(yscrollcommand=params_scrollbar.set)
        
        self.params_canvas.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        params_scrollbar.grid(row=3, column=2, sticky=(tk.N, tk.S), pady=5)
        
        # 运行按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=10)
        
        self.run_button = ttk.Button(button_frame, text="运行模型", command=self.run_model)
        self.run_button.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="重置参数", command=self.reset_parameters).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关于", command=self.show_about).pack(side=tk.RIGHT, padx=5)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding=5)
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = tk.Text(log_frame, height=10, width=80)
        log_scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 参数输入控件字典
        self.param_widgets = {}
    
    def load_models(self):
        """加载可用模型"""
        models = []
        self.model_files = {}
        
        models_info = self.config.get("models", [])
        for model_info in models_info:
            model_name = model_info.get("name", "")
            file_name = model_info.get("file_name", "")
            
            if os.path.exists(file_name):
                models.append(model_name)
                self.model_files[model_name] = file_name
        
        self.model_combo['values'] = models
        if models:
            self.model_combo.set(models[0])
            self.on_model_select()
    
    def on_model_select(self, event=None):
        """模型选择事件"""
        model_name = self.model_var.get()
        if not model_name:
            return
        
        # 清空现有参数控件
        for widget in self.params_scrollable_frame.winfo_children():
            widget.destroy()
        
        self.param_widgets.clear()
        
        # 这里应该解析FMW文件获取参数，简化版本只显示基本参数
        self.create_basic_parameters()
    
    def create_basic_parameters(self):
        """创建基本参数输入"""
        row = 0
        
        # 输入文件参数
        ttk.Label(self.params_scrollable_frame, text="输入文件:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        
        input_frame = ttk.Frame(self.params_scrollable_frame)
        input_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        
        self.input_file_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.input_file_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        def browse_input():
            file_path = filedialog.askopenfilename(title="选择输入文件")
            if file_path:
                self.input_file_var.set(file_path)
        
        ttk.Button(input_frame, text="浏览", command=browse_input).pack(side=tk.RIGHT, padx=(5, 0))
        
        row += 1
        
        # 输出目录参数
        ttk.Label(self.params_scrollable_frame, text="输出目录:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        
        output_frame = ttk.Frame(self.params_scrollable_frame)
        output_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        
        self.output_dir_var = tk.StringVar(value=os.path.join(os.getcwd(), "output"))
        ttk.Entry(output_frame, textvariable=self.output_dir_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        def browse_output():
            dir_path = filedialog.askdirectory(title="选择输出目录")
            if dir_path:
                self.output_dir_var.set(dir_path)
        
        ttk.Button(output_frame, text="浏览", command=browse_output).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 配置列权重
        self.params_scrollable_frame.columnconfigure(1, weight=1)
    
    def reset_parameters(self):
        """重置参数"""
        self.input_file_var.set("")
        self.output_dir_var.set(os.path.join(os.getcwd(), "output"))
    
    def run_model(self):
        """运行模型"""
        model_name = self.model_var.get()
        if not model_name:
            messagebox.showwarning("警告", "请选择一个模型")
            return
        
        input_file = self.input_file_var.get().strip()
        if not input_file:
            messagebox.showwarning("警告", "请选择输入文件")
            return
        
        if not os.path.exists(input_file):
            messagebox.showerror("错误", "输入文件不存在")
            return
        
        output_dir = self.output_dir_var.get().strip()
        if not output_dir:
            messagebox.showwarning("警告", "请选择输出目录")
            return
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 更新许可证使用次数
        self.update_license_usage()
        
        # 运行FME
        self.run_fme_model(model_name, input_file, output_dir)
    
    def run_fme_model(self, model_name, input_file, output_dir):
        """运行FME模型"""
        try:
            fme_path = self.config.get("fme_path", "fme.exe")
            model_file = self.model_files.get(model_name)
            
            if not model_file:
                messagebox.showerror("错误", "找不到模型文件")
                return
            
            # 构建命令
            cmd = [fme_path, model_file, f"--SourceDataset={input_file}", f"--DestDataset={output_dir}"]
            
            self.log(f"开始运行模型: {model_name}")
            self.log(f"输入文件: {input_file}")
            self.log(f"输出目录: {output_dir}")
            self.log(f"执行命令: {' '.join(cmd)}")
            
            self.progress.start()
            self.run_button.config(state=tk.DISABLED)
            
            # 在新线程中运行
            import threading
            
            def run_process():
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
                    
                    self.root.after(0, lambda: self.on_process_complete(result, output_dir))
                    
                except Exception as e:
                    self.root.after(0, lambda: self.on_process_error(str(e)))
            
            thread = threading.Thread(target=run_process)
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            self.log(f"运行模型失败: {e}")
            messagebox.showerror("错误", f"运行模型失败: {e}")
    
    def on_process_complete(self, result, output_dir):
        """进程完成回调"""
        self.progress.stop()
        self.run_button.config(state=tk.NORMAL)
        
        if result.returncode == 0:
            self.log("模型运行完成")
            if result.stdout:
                self.log(f"输出: {result.stdout}")
            
            if messagebox.askyesno("完成", "模型运行完成！是否打开输出目录？"):
                try:
                    os.startfile(output_dir)
                except:
                    pass
        else:
            self.log(f"模型运行失败，返回码: {result.returncode}")
            if result.stderr:
                self.log(f"错误: {result.stderr}")
            messagebox.showerror("错误", f"模型运行失败:\n{result.stderr}")
    
    def on_process_error(self, error):
        """进程错误回调"""
        self.progress.stop()
        self.run_button.config(state=tk.NORMAL)
        self.log(f"运行出错: {error}")
        messagebox.showerror("错误", f"运行出错: {error}")
    
    def update_license_usage(self):
        """更新许可证使用次数"""
        if self.license and "max_uses" in self.license:
            self.license["current_uses"] = self.license.get("current_uses", 0) + 1
            
            try:
                # 重新加密并保存许可证
                from cryptography.fernet import Fernet
                
                # 读取原始许可证文件获取密钥
                with open("license.json", "rb") as f:
                    data = base64.b64decode(f.read())
                
                key = data[:44]
                fernet = Fernet(key)
                
                # 加密更新后的许可证
                license_json = json.dumps(self.license, ensure_ascii=False)
                encrypted_license = fernet.encrypt(license_json.encode())
                
                # 保存
                with open("license.json", "wb") as f:
                    f.write(base64.b64encode(key + encrypted_license))
                    
            except Exception as e:
                self.log(f"更新许可证失败: {e}")
    
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def show_about(self):
        """显示关于信息"""
        about_text = f"""FME模型运行客户端

客户端名称: {self.config.get('client_name', '未知')}
创建时间: {self.config.get('created_at', '未知')}
包含模型: {len(self.config.get('models', []))}个

许可证信息:
- 许可证ID: {self.license.get('license_id', '未知')}
- 过期时间: {self.license.get('expire_date', '无限制')}
- 使用次数: {self.license.get('current_uses', 0)}/{self.license.get('max_uses', '无限制')}
"""
        messagebox.showinfo("关于", about_text)
    
    def run(self):
        """运行客户端"""
        self.root.mainloop()

if __name__ == "__main__":
    try:
        client = FMEClient()
        client.run()
    except Exception as e:
        messagebox.showerror("错误", f"客户端启动失败: {e}")
        sys.exit(1)
