"""
FME模型运行客户端
专为最终用户设计的简化版本
"""
import os
import sys
import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import subprocess
from datetime import datetime
import platform
import hashlib
import base64
from pathlib import Path
import threading
import time

class FMEClient:
    """FME客户端主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("FME模型运行客户端")
        self.root.geometry("800x600")

        # 居中显示
        self.center_window()

        # 初始化变量
        self.license_data = None
        self.machine_id = self.get_machine_id()
        self.is_registered = False

        # 检查注册状态
        if self.check_registration():
            self.show_main_interface()
        else:
            self.show_registration_interface()

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (400)
        y = (self.root.winfo_screenheight() // 2) - (300)
        self.root.geometry(f"800x600+{x}+{y}")

    def load_available_models(self):
        """加载可用模型"""
        self.model_listbox.delete(0, tk.END)

        models_dir = "models"
        if os.path.exists(models_dir):
            for item in os.listdir(models_dir):
                if item.endswith('.fmw') or item.endswith('.encrypted'):
                    # 显示友好的模型名称
                    display_name = os.path.splitext(item)[0]
                    if display_name.endswith('.fmw'):
                        display_name = display_name[:-4]

                    self.model_listbox.insert(tk.END, display_name)

        if self.model_listbox.size() == 0:
            self.model_listbox.insert(tk.END, "没有可用的模型")

    def on_model_select(self, event=None):
        """模型选择事件"""
        selection = self.model_listbox.curselection()
        if not selection:
            return

        model_name = self.model_listbox.get(selection[0])
        if model_name == "没有可用的模型":
            return

        # 显示模型信息
        self.show_model_info(model_name)

        # 创建参数界面
        self.create_parameter_interface(model_name)

        # 启用运行按钮
        self.run_button.config(state=tk.NORMAL)

    def show_model_info(self, model_name):
        """显示模型信息"""
        self.model_info_text.config(state=tk.NORMAL)
        self.model_info_text.delete(1.0, tk.END)

        # 查找模型文件
        model_file = None
        models_dir = "models"

        for ext in ['.fmw', '.fmw.encrypted']:
            potential_file = os.path.join(models_dir, model_name + ext)
            if os.path.exists(potential_file):
                model_file = potential_file
                break

        if model_file:
            file_size = os.path.getsize(model_file)
            file_size_mb = file_size / (1024 * 1024)

            is_encrypted = model_file.endswith('.encrypted')

            info_text = f"""模型名称: {model_name}
文件路径: {model_file}
文件大小: {file_size_mb:.2f} MB
加密状态: {'已加密' if is_encrypted else '未加密'}
修改时间: {datetime.fromtimestamp(os.path.getmtime(model_file)).strftime('%Y-%m-%d %H:%M:%S')}

说明: 此模型用于数据处理和转换"""

            self.model_info_text.insert(1.0, info_text)
        else:
            self.model_info_text.insert(1.0, f"模型文件不存在: {model_name}")

        self.model_info_text.config(state=tk.DISABLED)

    def create_parameter_interface(self, model_name):
        """创建参数界面"""
        # 清空现有参数控件
        for widget in self.params_scrollable_frame.winfo_children():
            widget.destroy()

        self.param_widgets.clear()

        # 创建基本参数（简化版本）
        row = 0

        # 输入文件参数
        ttk.Label(self.params_scrollable_frame, text="输入文件:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)

        input_frame = ttk.Frame(self.params_scrollable_frame)
        input_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)

        self.input_file_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.input_file_var).pack(side=tk.LEFT, fill=tk.X, expand=True)

        def browse_input():
            file_path = filedialog.askopenfilename(
                title="选择输入文件",
                filetypes=[
                    ("Shapefile", "*.shp"),
                    ("AutoCAD", "*.dwg"),
                    ("GeoJSON", "*.geojson"),
                    ("所有文件", "*.*")
                ]
            )
            if file_path:
                self.input_file_var.set(file_path)

        ttk.Button(input_frame, text="浏览", command=browse_input).pack(side=tk.RIGHT, padx=(5, 0))

        self.param_widgets["INPUT_FILE"] = self.input_file_var
        row += 1

        # 输出目录参数
        ttk.Label(self.params_scrollable_frame, text="输出目录:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)

        output_frame = ttk.Frame(self.params_scrollable_frame)
        output_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)

        self.output_dir_var = tk.StringVar(value=os.path.join(os.getcwd(), "output"))
        ttk.Entry(output_frame, textvariable=self.output_dir_var).pack(side=tk.LEFT, fill=tk.X, expand=True)

        def browse_output():
            dir_path = filedialog.askdirectory(title="选择输出目录")
            if dir_path:
                self.output_dir_var.set(dir_path)

        ttk.Button(output_frame, text="浏览", command=browse_output).pack(side=tk.RIGHT, padx=(5, 0))

        self.param_widgets["OUTPUT_DIR"] = self.output_dir_var
        row += 1

        # 配置列权重
        self.params_scrollable_frame.columnconfigure(1, weight=1)

    def get_machine_id(self):
        """获取机器唯一标识"""
        machine_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
        return hashlib.md5(machine_info.encode()).hexdigest()

    def check_registration(self):
        """检查注册状态"""
        try:
            if os.path.exists("license.dat"):
                with open("license.dat", "rb") as f:
                    encrypted_data = base64.b64decode(f.read())

                # 简单解密（实际应用中应使用更安全的方法）
                key = b"FME_CLIENT_KEY_2024"  # 固定密钥
                decrypted_data = bytes(a ^ b for a, b in zip(encrypted_data, key * (len(encrypted_data) // len(key) + 1)))

                self.license_data = json.loads(decrypted_data.decode())

                # 验证许可证
                if self.validate_license():
                    self.is_registered = True
                    return True
        except Exception as e:
            print(f"检查注册状态失败: {e}")

        return False

    def validate_license(self):
        """验证许可证"""
        if not self.license_data:
            return False

        try:
            # 检查过期时间
            if "expire_date" in self.license_data:
                expire_date = datetime.fromisoformat(self.license_data["expire_date"])
                if datetime.now() > expire_date:
                    messagebox.showerror("许可证过期", "许可证已过期，请联系管理员")
                    return False

            # 检查使用次数
            if "max_uses" in self.license_data:
                current_uses = self.license_data.get("current_uses", 0)
                max_uses = self.license_data["max_uses"]
                if current_uses >= max_uses:
                    messagebox.showerror("使用次数超限", "许可证使用次数已达上限")
                    return False

            # 检查机器码
            if "allowed_machines" in self.license_data:
                if self.machine_id not in self.license_data["allowed_machines"]:
                    messagebox.showerror("机器码不匹配", "此机器无权使用该客户端")
                    return False

            return True

        except Exception as e:
            print(f"验证许可证失败: {e}")
            return False
    
    def show_registration_interface(self):
        """显示注册界面"""
        # 清空窗口
        for widget in self.root.winfo_children():
            widget.destroy()

        # 主框架
        main_frame = ttk.Frame(self.root, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="FME模型运行客户端",
                               font=("Arial", 18, "bold"))
        title_label.pack(pady=(0, 30))

        # 机器码显示
        machine_frame = ttk.LabelFrame(main_frame, text="机器信息", padding=15)
        machine_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Label(machine_frame, text="机器码:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        machine_code_label = ttk.Label(machine_frame, text=self.machine_id,
                                      font=("Consolas", 12), foreground="blue")
        machine_code_label.pack(anchor=tk.W, pady=(5, 10))

        ttk.Label(machine_frame, text="请将此机器码提供给管理员以获取注册码",
                 font=("Arial", 9), foreground="gray").pack(anchor=tk.W)

        # 复制机器码按钮
        def copy_machine_id():
            self.root.clipboard_clear()
            self.root.clipboard_append(self.machine_id)
            messagebox.showinfo("提示", "机器码已复制到剪贴板")

        ttk.Button(machine_frame, text="复制机器码",
                  command=copy_machine_id).pack(anchor=tk.W, pady=(10, 0))

        # 注册码输入
        reg_frame = ttk.LabelFrame(main_frame, text="注册激活", padding=15)
        reg_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Label(reg_frame, text="注册码:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))

        self.reg_code_var = tk.StringVar()
        reg_entry = ttk.Entry(reg_frame, textvariable=self.reg_code_var,
                             font=("Consolas", 10), width=50)
        reg_entry.pack(fill=tk.X, pady=(0, 15))

        # 注册按钮
        button_frame = ttk.Frame(reg_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="激活",
                  command=self.activate_license).pack(side=tk.LEFT)

        ttk.Button(button_frame, text="退出",
                  command=self.root.quit).pack(side=tk.RIGHT)

        # 状态信息
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(20, 0))

        self.status_label = ttk.Label(status_frame, text="请输入注册码进行激活",
                                     font=("Arial", 9), foreground="orange")
        self.status_label.pack()

    def activate_license(self):
        """激活许可证"""
        reg_code = self.reg_code_var.get().strip()
        if not reg_code:
            messagebox.showwarning("警告", "请输入注册码")
            return

        try:
            self.status_label.config(text="正在验证注册码...", foreground="blue")
            self.root.update()

            # 解析注册码（Base64编码的JSON）
            try:
                decoded_data = base64.b64decode(reg_code.encode())
                license_data = json.loads(decoded_data.decode())
            except:
                messagebox.showerror("错误", "注册码格式无效")
                self.status_label.config(text="注册码格式无效", foreground="red")
                return

            # 验证机器码
            if "allowed_machines" in license_data:
                if self.machine_id not in license_data["allowed_machines"]:
                    messagebox.showerror("错误", "注册码与当前机器不匹配")
                    self.status_label.config(text="机器码不匹配", foreground="red")
                    return

            # 保存许可证
            self.license_data = license_data
            self.save_license()

            messagebox.showinfo("成功", "激活成功！程序将重新启动")
            self.is_registered = True
            self.show_main_interface()

        except Exception as e:
            messagebox.showerror("错误", f"激活失败: {e}")
            self.status_label.config(text="激活失败", foreground="red")

    def save_license(self):
        """保存许可证到本地"""
        try:
            license_json = json.dumps(self.license_data)

            # 简单加密
            key = b"FME_CLIENT_KEY_2024"
            encrypted_data = bytes(a ^ b for a, b in zip(license_json.encode(), key * (len(license_json) // len(key) + 1)))

            with open("license.dat", "wb") as f:
                f.write(base64.b64encode(encrypted_data))

        except Exception as e:
            print(f"保存许可证失败: {e}")
    
    def show_main_interface(self):
        """显示主界面"""
        # 清空窗口
        for widget in self.root.winfo_children():
            widget.destroy()

        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题栏
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(title_frame, text="FME模型运行客户端",
                 font=("Arial", 16, "bold")).pack(side=tk.LEFT)

        # 许可证信息
        license_info = f"许可证: {self.license_data.get('client_name', '未知')}"
        if "expire_date" in self.license_data:
            expire_date = self.license_data["expire_date"][:10]  # 只显示日期部分
            license_info += f" | 到期: {expire_date}"

        ttk.Label(title_frame, text=license_info,
                 font=("Arial", 9), foreground="gray").pack(side=tk.RIGHT)

        # 创建选项卡
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # 模型运行选项卡
        self.create_model_tab(notebook)

        # 运行日志选项卡
        self.create_log_tab(notebook)

        # 系统信息选项卡
        self.create_info_tab(notebook)

    def create_model_tab(self, notebook):
        """创建模型运行选项卡"""
        model_frame = ttk.Frame(notebook)
        notebook.add(model_frame, text="模型运行")

        # 左右分栏
        paned = ttk.PanedWindow(model_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 左侧 - 模型选择
        left_frame = ttk.LabelFrame(paned, text="模型选择", padding=10)
        paned.add(left_frame, weight=1)

        ttk.Label(left_frame, text="可用模型:").pack(anchor=tk.W, pady=(0, 5))

        # 模型列表
        self.model_listbox = tk.Listbox(left_frame, height=8)
        self.model_listbox.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        self.model_listbox.bind('<<ListboxSelect>>', self.on_model_select)

        # 加载模型
        self.load_available_models()

        # 模型信息显示
        self.model_info_text = tk.Text(left_frame, height=6, wrap=tk.WORD, state=tk.DISABLED)
        self.model_info_text.pack(fill=tk.X, pady=(0, 10))

        # 右侧 - 参数设置
        right_frame = ttk.LabelFrame(paned, text="参数设置", padding=10)
        paned.add(right_frame, weight=2)

        # 参数滚动框架
        self.params_canvas = tk.Canvas(right_frame, height=300)
        params_scrollbar = ttk.Scrollbar(right_frame, orient="vertical",
                                        command=self.params_canvas.yview)
        self.params_scrollable_frame = ttk.Frame(self.params_canvas)

        self.params_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.params_canvas.configure(scrollregion=self.params_canvas.bbox("all"))
        )

        self.params_canvas.create_window((0, 0), window=self.params_scrollable_frame, anchor="nw")
        self.params_canvas.configure(yscrollcommand=params_scrollbar.set)

        self.params_canvas.pack(side="left", fill="both", expand=True)
        params_scrollbar.pack(side="right", fill="y")

        # 运行按钮
        button_frame = ttk.Frame(right_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        self.run_button = ttk.Button(button_frame, text="运行模型",
                                    command=self.run_selected_model, state=tk.DISABLED)
        self.run_button.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="重置参数",
                  command=self.reset_parameters).pack(side=tk.LEFT)

        # 进度条
        self.progress = ttk.Progressbar(right_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=(10, 0))

        # 参数控件字典
        self.param_widgets = {}

    def create_log_tab(self, notebook):
        """创建运行日志选项卡"""
        log_frame = ttk.Frame(notebook)
        notebook.add(log_frame, text="运行日志")

        # 日志控制栏
        control_frame = ttk.Frame(log_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(control_frame, text="清除日志",
                  command=self.clear_log).pack(side=tk.LEFT)

        ttk.Button(control_frame, text="保存日志",
                  command=self.save_log).pack(side=tk.LEFT, padx=(5, 0))

        # 日志显示区域
        log_main_frame = ttk.Frame(log_frame)
        log_main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))

        self.log_text = tk.Text(log_main_frame, wrap=tk.WORD, font=("Consolas", 9))
        log_scrollbar = ttk.Scrollbar(log_main_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side="left", fill="both", expand=True)
        log_scrollbar.pack(side="right", fill="y")

        # 添加初始日志
        self.log(f"客户端启动成功 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.log(f"机器码: {self.machine_id}")
        self.log(f"许可证: {self.license_data.get('client_name', '未知')}")

    def create_info_tab(self, notebook):
        """创建系统信息选项卡"""
        info_frame = ttk.Frame(notebook)
        notebook.add(info_frame, text="系统信息")

        info_main_frame = ttk.Frame(info_frame, padding=20)
        info_main_frame.pack(fill=tk.BOTH, expand=True)

        # 系统信息
        system_frame = ttk.LabelFrame(info_main_frame, text="系统信息", padding=15)
        system_frame.pack(fill=tk.X, pady=(0, 15))

        system_info = f"""机器名: {platform.node()}
操作系统: {platform.system()} {platform.release()}
处理器: {platform.processor()}
架构: {platform.machine()}
Python版本: {platform.python_version()}
机器码: {self.machine_id}"""

        ttk.Label(system_frame, text=system_info, font=("Consolas", 9)).pack(anchor=tk.W)

        # 许可证信息
        license_frame = ttk.LabelFrame(info_main_frame, text="许可证信息", padding=15)
        license_frame.pack(fill=tk.X, pady=(0, 15))

        license_info = f"""许可证ID: {self.license_data.get('license_id', '未知')}
客户端名称: {self.license_data.get('client_name', '未知')}
创建时间: {self.license_data.get('created_at', '未知')}
过期时间: {self.license_data.get('expire_date', '无限制')}
最大使用次数: {self.license_data.get('max_uses', '无限制')}
当前使用次数: {self.license_data.get('current_uses', 0)}"""

        ttk.Label(license_frame, text=license_info, font=("Consolas", 9)).pack(anchor=tk.W)

        # 操作按钮
        action_frame = ttk.Frame(info_main_frame)
        action_frame.pack(fill=tk.X, pady=(15, 0))

        ttk.Button(action_frame, text="重新注册",
                  command=self.reregister).pack(side=tk.LEFT)

        ttk.Button(action_frame, text="关于",
                  command=self.show_about).pack(side=tk.RIGHT)

    def run_selected_model(self):
        """运行选中的模型"""
        selection = self.model_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个模型")
            return

        model_name = self.model_listbox.get(selection[0])

        # 验证参数
        input_file = self.input_file_var.get().strip()
        if not input_file:
            messagebox.showwarning("警告", "请选择输入文件")
            return

        if not os.path.exists(input_file):
            messagebox.showerror("错误", "输入文件不存在")
            return

        output_dir = self.output_dir_var.get().strip()
        if not output_dir:
            messagebox.showwarning("警告", "请选择输出目录")
            return

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 更新许可证使用次数
        self.update_license_usage()

        # 运行模型
        self.run_fme_model(model_name, input_file, output_dir)

    def run_fme_model(self, model_name, input_file, output_dir):
        """运行FME模型"""
        try:
            # 查找FME可执行文件
            fme_paths = [
                r"C:\Program Files\FME\fme.exe",
                r"C:\Program Files (x86)\FME\fme.exe",
                "fme.exe"  # 假设在PATH中
            ]

            fme_path = None
            for path in fme_paths:
                if os.path.exists(path):
                    fme_path = path
                    break

            if not fme_path:
                fme_path = "fme.exe"  # 尝试使用PATH中的fme

            # 查找模型文件
            models_dir = "models"
            model_file = None

            for ext in ['.fmw', '.fmw.encrypted']:
                potential_file = os.path.join(models_dir, model_name + ext)
                if os.path.exists(potential_file):
                    model_file = potential_file
                    break

            if not model_file:
                messagebox.showerror("错误", f"找不到模型文件: {model_name}")
                return

            # 如果是加密文件，需要先解密
            if model_file.endswith('.encrypted'):
                self.log("检测到加密模型，正在解密...")
                decrypted_file = self.decrypt_model_file(model_file)
                if decrypted_file:
                    model_file = decrypted_file
                else:
                    messagebox.showerror("错误", "模型解密失败")
                    return

            # 构建命令
            cmd = [fme_path, model_file]

            # 添加参数
            for param_name, param_var in self.param_widgets.items():
                if hasattr(param_var, 'get'):
                    value = param_var.get()
                    if value:
                        cmd.extend([f"--{param_name}", value])

            self.log(f"开始运行模型: {model_name}")
            self.log(f"输入文件: {input_file}")
            self.log(f"输出目录: {output_dir}")
            self.log(f"执行命令: {' '.join(cmd)}")

            self.progress.start()
            self.run_button.config(state=tk.DISABLED)

            # 在新线程中运行
            def run_process():
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True,
                                          encoding='utf-8', timeout=300)  # 5分钟超时

                    self.root.after(0, lambda: self.on_process_complete(result, output_dir))

                except subprocess.TimeoutExpired:
                    self.root.after(0, lambda: self.on_process_error("运行超时（5分钟）"))
                except Exception as e:
                    self.root.after(0, lambda: self.on_process_error(str(e)))

            thread = threading.Thread(target=run_process)
            thread.daemon = True
            thread.start()

        except Exception as e:
            self.log(f"运行模型失败: {e}")
            messagebox.showerror("错误", f"运行模型失败: {e}")

    def decrypt_model_file(self, encrypted_file):
        """解密模型文件"""
        try:
            # 简单的解密逻辑（实际应用中应使用更安全的方法）
            with open(encrypted_file, 'rb') as f:
                encrypted_data = f.read()

            # 使用固定密钥解密（与主程序保持一致）
            key = b"FME_ENCRYPTION_KEY_2024_SECURE"
            decrypted_data = bytes(a ^ b for a, b in zip(encrypted_data, key * (len(encrypted_data) // len(key) + 1)))

            # 保存到临时文件
            temp_file = encrypted_file.replace('.encrypted', '_temp.fmw')
            with open(temp_file, 'wb') as f:
                f.write(decrypted_data)

            return temp_file

        except Exception as e:
            self.log(f"解密模型文件失败: {e}")
            return None

    def on_process_complete(self, result, output_dir):
        """进程完成回调"""
        self.progress.stop()
        self.run_button.config(state=tk.NORMAL)

        if result.returncode == 0:
            self.log("模型运行完成")
            if result.stdout:
                self.log(f"输出: {result.stdout}")

            if messagebox.askyesno("完成", "模型运行完成！是否打开输出目录？"):
                try:
                    if platform.system() == "Windows":
                        os.startfile(output_dir)
                    else:
                        subprocess.run(["open", output_dir])  # macOS
                except:
                    pass
        else:
            self.log(f"模型运行失败，返回码: {result.returncode}")
            if result.stderr:
                self.log(f"错误: {result.stderr}")
            messagebox.showerror("错误", f"模型运行失败:\n{result.stderr}")

    def on_process_error(self, error):
        """进程错误回调"""
        self.progress.stop()
        self.run_button.config(state=tk.NORMAL)
        self.log(f"运行出错: {error}")
        messagebox.showerror("错误", f"运行出错: {error}")

    def reset_parameters(self):
        """重置参数"""
        self.input_file_var.set("")
        self.output_dir_var.set(os.path.join(os.getcwd(), "output"))

    def update_license_usage(self):
        """更新许可证使用次数"""
        if self.license_data and "max_uses" in self.license_data:
            self.license_data["current_uses"] = self.license_data.get("current_uses", 0) + 1
            self.save_license()

    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def clear_log(self):
        """清除日志"""
        if messagebox.askyesno("确认", "确定要清除所有日志吗？"):
            self.log_text.delete(1.0, tk.END)

    def save_log(self):
        """保存日志"""
        file_path = filedialog.asksaveasfilename(
            title="保存日志",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialvalue=f"fme_client_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("成功", f"日志已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存日志失败: {e}")

    def reregister(self):
        """重新注册"""
        if messagebox.askyesno("确认", "确定要重新注册吗？这将清除当前的许可证信息。"):
            try:
                if os.path.exists("license.dat"):
                    os.remove("license.dat")
                self.license_data = None
                self.is_registered = False
                self.show_registration_interface()
            except Exception as e:
                messagebox.showerror("错误", f"重新注册失败: {e}")

    def show_about(self):
        """显示关于信息"""
        about_text = f"""FME模型运行客户端 v1.0

客户端信息:
• 许可证: {self.license_data.get('client_name', '未知')}
• 机器码: {self.machine_id}
• 创建时间: {self.license_data.get('created_at', '未知')}

功能特性:
• 自动机器码检测
• 注册码激活
• 模型运行管理
• 运行日志记录
• 加密模型支持

技术支持:
如有问题请联系管理员
"""
        messagebox.showinfo("关于", about_text)

    def run(self):
        """运行客户端"""
        self.root.mainloop()

if __name__ == "__main__":
    try:
        client = FMEClient()
        client.run()
    except Exception as e:
        messagebox.showerror("错误", f"客户端启动失败: {e}")
        sys.exit(1)
    
    def get_machine_id(self):
        """获取机器唯一标识"""
        machine_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
        return hashlib.md5(machine_info.encode()).hexdigest()
    
    def validate_license(self):
        """验证许可证"""
        if not self.license or not self.license.get("is_active"):
            return False
        
        # 检查过期时间
        if self.license.get("expire_date"):
            try:
                expire_date = datetime.fromisoformat(self.license["expire_date"])
                if datetime.now() > expire_date:
                    messagebox.showerror("许可证过期", "许可证已过期")
                    return False
            except:
                return False
        
        # 检查使用次数
        max_uses = self.license.get("max_uses")
        if max_uses and self.license.get("current_uses", 0) >= max_uses:
            messagebox.showerror("使用次数超限", "许可证使用次数已达上限")
            return False
        
        # 检查机器码
        allowed_machines = self.license.get("allowed_machines", [])
        if allowed_machines:
            machine_id = self.get_machine_id()
            if machine_id not in allowed_machines:
                messagebox.showerror("机器码不匹配", "此机器无权使用该客户端")
                return False
        
        return True
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="FME模型运行客户端", 
                               font=("", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 模型选择
        ttk.Label(main_frame, text="选择模型:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.model_var = tk.StringVar()
        self.model_combo = ttk.Combobox(main_frame, textvariable=self.model_var, 
                                       width=50, state="readonly")
        self.model_combo.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        self.model_combo.bind('<<ComboboxSelected>>', self.on_model_select)
        
        # 参数设置区域
        params_label = ttk.Label(main_frame, text="参数设置:")
        params_label.grid(row=2, column=0, sticky=(tk.W, tk.N), pady=5)
        
        # 参数框架（带滚动条）
        self.params_canvas = tk.Canvas(main_frame, height=200)
        params_scrollbar = ttk.Scrollbar(main_frame, orient="vertical", 
                                        command=self.params_canvas.yview)
        self.params_scrollable_frame = ttk.Frame(self.params_canvas)
        
        self.params_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.params_canvas.configure(scrollregion=self.params_canvas.bbox("all"))
        )
        
        self.params_canvas.create_window((0, 0), window=self.params_scrollable_frame, anchor="nw")
        self.params_canvas.configure(yscrollcommand=params_scrollbar.set)
        
        self.params_canvas.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        params_scrollbar.grid(row=3, column=2, sticky=(tk.N, tk.S), pady=5)
        
        # 运行按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=10)
        
        self.run_button = ttk.Button(button_frame, text="运行模型", command=self.run_model)
        self.run_button.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="重置参数", command=self.reset_parameters).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关于", command=self.show_about).pack(side=tk.RIGHT, padx=5)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding=5)
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = tk.Text(log_frame, height=10, width=80)
        log_scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 参数输入控件字典
        self.param_widgets = {}
    
    def load_models(self):
        """加载可用模型"""
        models = []
        self.model_files = {}
        
        models_info = self.config.get("models", [])
        for model_info in models_info:
            model_name = model_info.get("name", "")
            file_name = model_info.get("file_name", "")
            
            if os.path.exists(file_name):
                models.append(model_name)
                self.model_files[model_name] = file_name
        
        self.model_combo['values'] = models
        if models:
            self.model_combo.set(models[0])
            self.on_model_select()
    
    def on_model_select(self, event=None):
        """模型选择事件"""
        model_name = self.model_var.get()
        if not model_name:
            return
        
        # 清空现有参数控件
        for widget in self.params_scrollable_frame.winfo_children():
            widget.destroy()
        
        self.param_widgets.clear()
        
        # 这里应该解析FMW文件获取参数，简化版本只显示基本参数
        self.create_basic_parameters()
    
    def create_basic_parameters(self):
        """创建基本参数输入"""
        row = 0
        
        # 输入文件参数
        ttk.Label(self.params_scrollable_frame, text="输入文件:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        
        input_frame = ttk.Frame(self.params_scrollable_frame)
        input_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        
        self.input_file_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.input_file_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        def browse_input():
            file_path = filedialog.askopenfilename(title="选择输入文件")
            if file_path:
                self.input_file_var.set(file_path)
        
        ttk.Button(input_frame, text="浏览", command=browse_input).pack(side=tk.RIGHT, padx=(5, 0))
        
        row += 1
        
        # 输出目录参数
        ttk.Label(self.params_scrollable_frame, text="输出目录:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        
        output_frame = ttk.Frame(self.params_scrollable_frame)
        output_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        
        self.output_dir_var = tk.StringVar(value=os.path.join(os.getcwd(), "output"))
        ttk.Entry(output_frame, textvariable=self.output_dir_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        def browse_output():
            dir_path = filedialog.askdirectory(title="选择输出目录")
            if dir_path:
                self.output_dir_var.set(dir_path)
        
        ttk.Button(output_frame, text="浏览", command=browse_output).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 配置列权重
        self.params_scrollable_frame.columnconfigure(1, weight=1)
    
    def reset_parameters(self):
        """重置参数"""
        self.input_file_var.set("")
        self.output_dir_var.set(os.path.join(os.getcwd(), "output"))
    
    def run_model(self):
        """运行模型"""
        model_name = self.model_var.get()
        if not model_name:
            messagebox.showwarning("警告", "请选择一个模型")
            return
        
        input_file = self.input_file_var.get().strip()
        if not input_file:
            messagebox.showwarning("警告", "请选择输入文件")
            return
        
        if not os.path.exists(input_file):
            messagebox.showerror("错误", "输入文件不存在")
            return
        
        output_dir = self.output_dir_var.get().strip()
        if not output_dir:
            messagebox.showwarning("警告", "请选择输出目录")
            return
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 更新许可证使用次数
        self.update_license_usage()
        
        # 运行FME
        self.run_fme_model(model_name, input_file, output_dir)
    
    def run_fme_model(self, model_name, input_file, output_dir):
        """运行FME模型"""
        try:
            fme_path = self.config.get("fme_path", "fme.exe")
            model_file = self.model_files.get(model_name)
            
            if not model_file:
                messagebox.showerror("错误", "找不到模型文件")
                return
            
            # 构建命令
            cmd = [fme_path, model_file, f"--SourceDataset={input_file}", f"--DestDataset={output_dir}"]
            
            self.log(f"开始运行模型: {model_name}")
            self.log(f"输入文件: {input_file}")
            self.log(f"输出目录: {output_dir}")
            self.log(f"执行命令: {' '.join(cmd)}")
            
            self.progress.start()
            self.run_button.config(state=tk.DISABLED)
            
            # 在新线程中运行
            import threading
            
            def run_process():
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
                    
                    self.root.after(0, lambda: self.on_process_complete(result, output_dir))
                    
                except Exception as e:
                    self.root.after(0, lambda: self.on_process_error(str(e)))
            
            thread = threading.Thread(target=run_process)
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            self.log(f"运行模型失败: {e}")
            messagebox.showerror("错误", f"运行模型失败: {e}")
    
    def on_process_complete(self, result, output_dir):
        """进程完成回调"""
        self.progress.stop()
        self.run_button.config(state=tk.NORMAL)
        
        if result.returncode == 0:
            self.log("模型运行完成")
            if result.stdout:
                self.log(f"输出: {result.stdout}")
            
            if messagebox.askyesno("完成", "模型运行完成！是否打开输出目录？"):
                try:
                    os.startfile(output_dir)
                except:
                    pass
        else:
            self.log(f"模型运行失败，返回码: {result.returncode}")
            if result.stderr:
                self.log(f"错误: {result.stderr}")
            messagebox.showerror("错误", f"模型运行失败:\n{result.stderr}")
    
    def on_process_error(self, error):
        """进程错误回调"""
        self.progress.stop()
        self.run_button.config(state=tk.NORMAL)
        self.log(f"运行出错: {error}")
        messagebox.showerror("错误", f"运行出错: {error}")
    
    def update_license_usage(self):
        """更新许可证使用次数"""
        if self.license and "max_uses" in self.license:
            self.license["current_uses"] = self.license.get("current_uses", 0) + 1
            
            try:
                # 重新加密并保存许可证
                from cryptography.fernet import Fernet
                
                # 读取原始许可证文件获取密钥
                with open("license.json", "rb") as f:
                    data = base64.b64decode(f.read())
                
                key = data[:44]
                fernet = Fernet(key)
                
                # 加密更新后的许可证
                license_json = json.dumps(self.license, ensure_ascii=False)
                encrypted_license = fernet.encrypt(license_json.encode())
                
                # 保存
                with open("license.json", "wb") as f:
                    f.write(base64.b64encode(key + encrypted_license))
                    
            except Exception as e:
                self.log(f"更新许可证失败: {e}")
    
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def show_about(self):
        """显示关于信息"""
        about_text = f"""FME模型运行客户端

客户端名称: {self.config.get('client_name', '未知')}
创建时间: {self.config.get('created_at', '未知')}
包含模型: {len(self.config.get('models', []))}个

许可证信息:
- 许可证ID: {self.license.get('license_id', '未知')}
- 过期时间: {self.license.get('expire_date', '无限制')}
- 使用次数: {self.license.get('current_uses', 0)}/{self.license.get('max_uses', '无限制')}
"""
        messagebox.showinfo("关于", about_text)
    
    def run(self):
        """运行客户端"""
        self.root.mainloop()

if __name__ == "__main__":
    try:
        client = FMEClient()
        client.run()
    except Exception as e:
        messagebox.showerror("错误", f"客户端启动失败: {e}")
        sys.exit(1)
