"""
测试参数映射功能
"""
import os
import sys
import tempfile
import json
import base64

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def create_test_fmw_with_parameters():
    """创建包含各种参数类型的测试FMW文件"""
    
    # 创建测试参数数据
    test_parameters = {
        "parameters": [
            {
                "name": "INPUT_FILE",
                "type": "file",
                "prompt": "输入文件",
                "required": True,
                "defaultValue": "",
                "valueType": "string",
                "accessMode": "read",
                "itemsToSelect": "files",
                "filters": [
                    {
                        "name": "Shapefile",
                        "filter": ["*.shp"]
                    },
                    {
                        "name": "所有文件",
                        "filter": ["*.*"]
                    }
                ]
            },
            {
                "name": "OUTPUT_DIR",
                "type": "file",
                "prompt": "输出目录",
                "required": True,
                "defaultValue": "",
                "valueType": "string",
                "accessMode": "write",
                "itemsToSelect": "folders"
            },
            {
                "name": "COORDINATE_SYSTEM",
                "type": "dropdown",
                "prompt": "坐标系统",
                "required": False,
                "defaultValue": "EPSG:4326",
                "choiceSettings": {
                    "choices": [
                        {"display": "WGS84 (EPSG:4326)", "value": "EPSG:4326"},
                        {"display": "Web Mercator (EPSG:3857)", "value": "EPSG:3857"},
                        {"display": "Beijing 1954 (EPSG:2154)", "value": "EPSG:2154"}
                    ]
                }
            },
            {
                "name": "PRECISION",
                "type": "number",
                "prompt": "精度设置",
                "required": False,
                "defaultValue": "0.001",
                "valueType": "float"
            },
            {
                "name": "ENABLE_LOG",
                "type": "checkbox",
                "prompt": "启用详细日志",
                "required": False,
                "defaultValue": "true",
                "valueType": "boolean"
            },
            {
                "name": "DESCRIPTION",
                "type": "textarea",
                "prompt": "处理描述",
                "required": False,
                "defaultValue": "数据处理任务",
                "valueType": "string"
            },
            {
                "name": "PROCESSING_MODE",
                "type": "listbox",
                "prompt": "处理模式",
                "required": False,
                "defaultValue": "standard",
                "singleSelection": True,
                "choiceSettings": {
                    "choices": [
                        {"display": "快速模式", "value": "fast"},
                        {"display": "标准模式", "value": "standard"},
                        {"display": "高精度模式", "value": "precise"}
                    ]
                }
            },
            {
                "name": "FEATURE_TYPES",
                "type": "listbox",
                "prompt": "要素类型",
                "required": False,
                "defaultValue": "",
                "singleSelection": False,
                "delimiter": ",",
                "choiceSettings": {
                    "choices": [
                        {"display": "点", "value": "point"},
                        {"display": "线", "value": "line"},
                        {"display": "面", "value": "polygon"}
                    ]
                }
            },
            {
                "name": "PROCESS_DATE",
                "type": "datetime",
                "prompt": "处理日期",
                "required": False,
                "defaultValue": "2024-08-12 14:30:00",
                "valueType": "string"
            },
            {
                "name": "HIGHLIGHT_COLOR",
                "type": "color",
                "prompt": "高亮颜色",
                "required": False,
                "defaultValue": "#FF0000",
                "valueType": "string"
            }
        ]
    }
    
    # 将参数编码为Base64
    params_json = json.dumps(test_parameters, ensure_ascii=False)
    params_base64 = base64.b64encode(params_json.encode('utf-8')).decode('ascii')
    
    # 创建FMW文件内容
    fmw_content = f'''#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#!   Command line to run this workspace:
#!     "C:\\Program Files\\FME\\fme.exe" test_parameters.fmw
#!       --INPUT_FILE ""
#!       --OUTPUT_DIR ""
#!       --COORDINATE_SYSTEM "EPSG:4326"
#!       --PRECISION "0.001"
#!       --ENABLE_LOG "true"
#!       --DESCRIPTION ""
#!       --PROCESSING_MODE "standard"
#!       --FEATURE_TYPES ""
#!       --PROCESS_DATE ""
#!       --HIGHLIGHT_COLOR "#FF0000"
#!   
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   BEGIN_PYTHON=""
#!   BEGIN_TCL=""
#!   CATEGORY=""
#!   DESCRIPTION="测试参数映射的FMW文件"
#!   DESTINATION="NONE"
#!   DOC_EXTENTS="0 0"
#!   DOC_TOP_LEFT="0 0"
#!   END_PYTHON=""
#!   END_TCL=""
#!   FME_BUILD_NUM="23619"
#!   FME_DOCUMENT_GUID="test-parameter-mapping-guid"
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_NAMES_ENCODING="UTF-8"
#!   HISTORY=""
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0"
#!   LAST_SAVE_DATE="2024-08-12T14:30:00"
#!   LOG_FILE=""
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   PYTHON_COMPATIBILITY="37"
#!   SHOW_ANNOTATIONS="true"
#!   TITLE="参数映射测试模型"
#!   WORKSPACE_VERSION="1"
#!   XFORM_DEPRECATED_TRANSFORMERS=""
#! >
#! 
#! <USER_PARAMETERS FORM="{params_base64}">
#! </USER_PARAMETERS>
#! 
#! <DATASETS>
#! </DATASETS>
#! 
#! <DATA_TYPES>
#! </DATA_TYPES>
#! 
#! <WORKBENCH_TRANSFORMERS>
#! </WORKBENCH_TRANSFORMERS>
#! 
#! <FEAT_TYPES>
#! </FEAT_TYPES>
#! 
#! <FMESERVER>
#! </FMESERVER>
#! 
#! <GLOBAL_PARAMETERS>
#! </GLOBAL_PARAMETERS>
#! 
#! </WORKSPACE>'''
    
    return fmw_content

def test_parameter_mapping():
    """测试参数映射功能"""
    print("=" * 60)
    print("测试参数映射功能")
    print("=" * 60)
    
    # 创建测试FMW文件
    fmw_content = create_test_fmw_with_parameters()
    test_fmw_path = "test_parameter_mapping.fmw"
    
    with open(test_fmw_path, 'w', encoding='utf-8') as f:
        f.write(fmw_content)
    
    print(f"✓ 创建测试FMW文件: {test_fmw_path}")
    
    try:
        # 测试参数解析
        from parse_fmw import parse_fmw_parameters
        
        print("\n1. 测试参数解析...")
        result = parse_fmw_parameters(test_fmw_path, debug=True)
        
        if result and result.get('parameters'):
            parameters = result['parameters']
            print(f"✓ 成功解析到 {len(parameters)} 个参数")
            
            print("\n2. 参数详情:")
            for param in parameters:
                param_name = param.get('name', '')
                param_info = param.get('info', {})
                param_type = param_info.get('type', '')
                prompt = param_info.get('prompt', '')
                required = param_info.get('required', False)
                default_value = param_info.get('default_value', '')
                
                print(f"  - {param_name}: {prompt}")
                print(f"    类型: {param_type}")
                print(f"    必填: {'是' if required else '否'}")
                print(f"    默认值: {default_value}")
                
                # 显示选项（如果有）
                if param_type in ['dropdown', 'listbox']:
                    options = param_info.get('options', [])
                    if options:
                        print(f"    选项: {[opt.get('label', opt.get('value', '')) for opt in options]}")
                
                # 显示文件类型（如果有）
                if param_type == 'file':
                    file_types = param_info.get('file_types', [])
                    is_folder = param_info.get('is_folder', False)
                    access_mode = param_info.get('access_mode', 'read')
                    print(f"    文件类型: {file_types}")
                    print(f"    是否文件夹: {'是' if is_folder else '否'}")
                    print(f"    访问模式: {access_mode}")
                
                print()
            
            print("3. 测试组件映射...")
            
            # 导入主窗口类进行测试
            try:
                import tkinter as tk
                from main_window import MainWindow
                
                # 创建临时模型信息
                model_info = {
                    'id': 'test_model',
                    'name': '参数映射测试模型',
                    'file_path': test_fmw_path,
                    'parameters': parameters
                }
                
                print("✓ 参数映射测试准备完成")
                print("\n要查看图形界面中的参数映射效果，请运行:")
                print("  python main.py")
                print("然后导入测试FMW文件进行查看")
                
            except ImportError as e:
                print(f"✗ 无法导入主窗口类: {e}")
            
        else:
            print("✗ 参数解析失败")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        if os.path.exists(test_fmw_path):
            os.unlink(test_fmw_path)
            print(f"\n✓ 清理测试文件: {test_fmw_path}")

def create_ui_test():
    """创建UI测试界面"""
    print("\n4. 创建UI测试界面...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        root = tk.Tk()
        root.title("参数映射测试")
        root.geometry("800x600")
        
        # 创建测试FMW文件
        fmw_content = create_test_fmw_with_parameters()
        test_fmw_path = "ui_test_parameters.fmw"
        
        with open(test_fmw_path, 'w', encoding='utf-8') as f:
            f.write(fmw_content)
        
        # 解析参数
        from parse_fmw import parse_fmw_parameters
        result = parse_fmw_parameters(test_fmw_path, debug=False)
        
        if result and result.get('parameters'):
            parameters = result['parameters']
            
            # 创建主框架
            main_frame = ttk.Frame(root, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            ttk.Label(main_frame, text="参数映射测试界面", 
                     font=("Arial", 16, "bold")).pack(pady=(0, 20))
            
            # 创建滚动框架
            canvas = tk.Canvas(main_frame)
            scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas)
            
            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )
            
            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)
            
            # 创建参数控件
            param_widgets = {}
            
            # 导入主窗口类来使用其方法
            sys.path.insert(0, current_dir)
            from main_window import MainWindow
            
            # 创建临时主窗口实例来使用其方法
            temp_app = MainWindow()
            
            row = 0
            for param in parameters:
                param_name = param.get('name', '')
                param_info = param.get('info', {})
                
                if not param_name:
                    continue
                
                # 创建标签
                label_text = param_info.get('prompt', param_name)
                if param_info.get('required', False):
                    label_text += " *"
                
                label = ttk.Label(scrollable_frame, text=label_text)
                label.grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
                
                # 创建控件
                widget = temp_app.create_parameter_widget(scrollable_frame, param_info)
                widget.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
                
                param_widgets[param_name] = {
                    'widget': widget,
                    'info': param_info
                }
                
                row += 1
            
            # 配置列权重
            scrollable_frame.columnconfigure(1, weight=1)
            
            # 添加测试按钮
            def test_get_values():
                """测试获取参数值"""
                temp_app.param_widgets = param_widgets
                values = temp_app.get_parameter_values()
                
                # 显示结果
                result_window = tk.Toplevel(root)
                result_window.title("参数值测试结果")
                result_window.geometry("600x400")
                
                text_widget = tk.Text(result_window, wrap=tk.WORD)
                text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
                
                result_text = "获取到的参数值:\n\n"
                for name, value in values.items():
                    result_text += f"{name} = {value}\n"
                
                text_widget.insert(1.0, result_text)
                text_widget.config(state=tk.DISABLED)
            
            button_frame = ttk.Frame(scrollable_frame)
            button_frame.grid(row=row, column=0, columnspan=2, pady=20)
            
            ttk.Button(button_frame, text="测试获取参数值", 
                      command=test_get_values).pack(side=tk.LEFT, padx=5)
            
            ttk.Button(button_frame, text="关闭", 
                      command=root.destroy).pack(side=tk.LEFT, padx=5)
            
            # 布局滚动框架
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")
            
            print("✓ UI测试界面创建成功")
            print("  界面将显示所有参数类型的映射效果")
            
            root.mainloop()
        
        # 清理测试文件
        if os.path.exists(test_fmw_path):
            os.unlink(test_fmw_path)
            
    except Exception as e:
        print(f"✗ UI测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_parameter_mapping()
    
    # 询问是否运行UI测试
    try:
        response = input("\n是否运行UI测试界面? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            create_ui_test()
    except KeyboardInterrupt:
        print("\n测试结束")
    except:
        pass
