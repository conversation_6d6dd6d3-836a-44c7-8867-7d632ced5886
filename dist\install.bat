@echo off
chcp 65001 >nul
title FME模型管理工具安装程序

echo ========================================
echo FME模型管理工具安装程序
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限
) else (
    echo 注意: 建议以管理员身份运行此安装程序
)

echo.
echo 正在安装FME模型管理工具...
echo.

:: 创建程序目录
set INSTALL_DIR=%ProgramFiles%\FME模型管理工具
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
    echo 创建安装目录: %INSTALL_DIR%
)

:: 复制文件
echo 复制程序文件...
xcopy /E /I /Y . "%INSTALL_DIR%"

:: 创建桌面快捷方式
echo 创建桌面快捷方式...
set DESKTOP=%USERPROFILE%\Desktop
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%DESKTOP%\FME模型管理工具.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%INSTALL_DIR%\start.bat" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%INSTALL_DIR%" >> CreateShortcut.vbs
echo oLink.Description = "FME模型管理工具" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs >nul
del CreateShortcut.vbs

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 程序已安装到: %INSTALL_DIR%
echo 桌面快捷方式已创建
echo.
echo 双击桌面上的"FME模型管理工具"图标启动程序
echo.
pause
