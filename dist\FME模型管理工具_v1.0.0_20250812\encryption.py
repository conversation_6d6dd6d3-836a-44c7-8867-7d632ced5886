"""
FMW文件加密解密模块
"""
import os
import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import json
from datetime import datetime, timedelta
import platform
import uuid
import hashlib

class FMWEncryption:
    """FMW文件加密解密类"""
    
    def __init__(self, key_file="encryption.key"):
        self.key_file = key_file
        self.key = self.load_or_generate_key()
        
    def load_or_generate_key(self):
        """加载或生成加密密钥"""
        if os.path.exists(self.key_file):
            try:
                with open(self.key_file, 'rb') as f:
                    return f.read()
            except Exception as e:
                print(f"加载密钥失败: {e}")
                
        # 生成新密钥
        key = Fernet.generate_key()
        try:
            with open(self.key_file, 'wb') as f:
                f.write(key)
            print(f"生成新密钥: {self.key_file}")
        except Exception as e:
            print(f"保存密钥失败: {e}")
            
        return key
    
    def encrypt_file(self, file_path, output_path=None):
        """加密FMW文件"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        if output_path is None:
            output_path = file_path + ".encrypted"
            
        try:
            fernet = Fernet(self.key)
            
            with open(file_path, 'rb') as f:
                file_data = f.read()
                
            encrypted_data = fernet.encrypt(file_data)
            
            with open(output_path, 'wb') as f:
                f.write(encrypted_data)
                
            print(f"文件加密成功: {output_path}")
            return output_path
            
        except Exception as e:
            raise Exception(f"加密失败: {e}")
    
    def decrypt_file(self, encrypted_file_path, output_path=None):
        """解密FMW文件"""
        if not os.path.exists(encrypted_file_path):
            raise FileNotFoundError(f"加密文件不存在: {encrypted_file_path}")
            
        if output_path is None:
            output_path = encrypted_file_path.replace(".encrypted", "")
            
        try:
            fernet = Fernet(self.key)
            
            with open(encrypted_file_path, 'rb') as f:
                encrypted_data = f.read()
                
            decrypted_data = fernet.decrypt(encrypted_data)
            
            with open(output_path, 'wb') as f:
                f.write(decrypted_data)
                
            print(f"文件解密成功: {output_path}")
            return output_path
            
        except Exception as e:
            raise Exception(f"解密失败: {e}")

class LicenseManager:
    """许可证管理类"""
    
    def __init__(self):
        self.machine_id = self.get_machine_id()
        
    def get_machine_id(self):
        """获取机器唯一标识"""
        # 获取机器的唯一标识符
        machine_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
        return hashlib.md5(machine_info.encode()).hexdigest()
    
    def generate_license(self, expire_date=None, max_uses=None, allowed_machines=None):
        """生成许可证"""
        license_data = {
            "license_id": str(uuid.uuid4()),
            "created_at": datetime.now().isoformat(),
            "expire_date": expire_date.isoformat() if expire_date else None,
            "max_uses": max_uses,
            "current_uses": 0,
            "allowed_machines": allowed_machines or [self.machine_id],
            "is_active": True
        }
        
        return license_data
    
    def save_license(self, license_data, file_path):
        """保存许可证到文件"""
        try:
            # 加密许可证数据
            fernet = Fernet(Fernet.generate_key())
            license_json = json.dumps(license_data, ensure_ascii=False)
            encrypted_license = fernet.encrypt(license_json.encode())
            
            # 保存加密的许可证和密钥
            with open(file_path, 'wb') as f:
                f.write(base64.b64encode(fernet.key + encrypted_license))
                
            print(f"许可证保存成功: {file_path}")
            return True
            
        except Exception as e:
            print(f"保存许可证失败: {e}")
            return False
    
    def load_license(self, file_path):
        """从文件加载许可证"""
        try:
            with open(file_path, 'rb') as f:
                data = base64.b64decode(f.read())
                
            # 提取密钥和加密数据
            key = data[:44]  # Fernet密钥长度为44字节
            encrypted_data = data[44:]
            
            fernet = Fernet(key)
            decrypted_data = fernet.decrypt(encrypted_data)
            
            license_data = json.loads(decrypted_data.decode())
            return license_data
            
        except Exception as e:
            print(f"加载许可证失败: {e}")
            return None
    
    def validate_license(self, license_data):
        """验证许可证"""
        if not license_data or not license_data.get("is_active"):
            return False, "许可证无效或已禁用"
            
        # 检查过期时间
        if license_data.get("expire_date"):
            expire_date = datetime.fromisoformat(license_data["expire_date"])
            if datetime.now() > expire_date:
                return False, "许可证已过期"
        
        # 检查使用次数
        max_uses = license_data.get("max_uses")
        if max_uses and license_data.get("current_uses", 0) >= max_uses:
            return False, "许可证使用次数已达上限"
        
        # 检查机器码
        allowed_machines = license_data.get("allowed_machines", [])
        if allowed_machines and self.machine_id not in allowed_machines:
            return False, "机器码不匹配"
            
        return True, "许可证有效"
    
    def use_license(self, license_data, file_path):
        """使用许可证（增加使用次数）"""
        license_data["current_uses"] = license_data.get("current_uses", 0) + 1
        return self.save_license(license_data, file_path)
