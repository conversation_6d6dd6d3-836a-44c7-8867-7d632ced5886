"""
FMW文件运行模块
"""
import os
import subprocess
import threading
import time
import json
from datetime import datetime
from config import config
from parse_fmw import parse_fmw_parameters

class FMWRunner:
    """FMW文件运行器"""
    
    def __init__(self, fme_path=None):
        self.fme_path = fme_path or config.get("fme_path")
        self.running_processes = {}
        self.process_callbacks = {}
        
    def validate_fme_path(self):
        """验证FME路径是否有效"""
        if not self.fme_path or not os.path.exists(self.fme_path):
            return False, f"FME路径无效: {self.fme_path}"
        return True, "FME路径有效"
    
    def build_command(self, fmw_path, parameters=None, output_dir=None):
        """构建FME命令行"""
        if not os.path.exists(fmw_path):
            raise FileNotFoundError(f"FMW文件不存在: {fmw_path}")
            
        cmd = [self.fme_path, fmw_path]
        
        # 添加参数
        if parameters:
            for key, value in parameters.items():
                if value is not None and value != "":
                    cmd.extend([f'--{key}', str(value)])
        
        # 添加输出目录
        if output_dir:
            # 尝试解析FMW文件找到输出参数
            try:
                fmw_params = parse_fmw_parameters(fmw_path)
                if fmw_params and fmw_params.get('parameters_dict'):
                    for param_name, param_info in fmw_params['parameters_dict'].items():
                        if param_info.get('access_mode') == 'write':
                            cmd.extend([f'--{param_name}', output_dir])
                            break
            except Exception as e:
                print(f"解析输出参数失败: {e}")
                
        return cmd
    
    def run_fmw_sync(self, fmw_path, parameters=None, output_dir=None):
        """同步运行FMW文件"""
        try:
            cmd = self.build_command(fmw_path, parameters, output_dir)
            print(f"执行命令: {' '.join(cmd)}")
            
            start_time = time.time()
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            end_time = time.time()
            
            execution_time = end_time - start_time
            
            return {
                'success': result.returncode == 0,
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'execution_time': execution_time,
                'command': ' '.join(cmd)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'execution_time': 0
            }
    
    def run_fmw_async(self, task_id, fmw_path, parameters=None, output_dir=None, 
                      progress_callback=None, complete_callback=None):
        """异步运行FMW文件"""
        def run_task():
            try:
                cmd = self.build_command(fmw_path, parameters, output_dir)
                print(f"异步执行命令: {' '.join(cmd)}")
                
                start_time = time.time()
                
                # 启动进程
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8'
                )
                
                self.running_processes[task_id] = process
                
                # 监控进程输出
                while True:
                    output = process.stdout.readline()
                    if output == '' and process.poll() is not None:
                        break
                    if output and progress_callback:
                        progress_callback(task_id, output.strip())
                
                # 等待进程完成
                stdout, stderr = process.communicate()
                end_time = time.time()
                
                execution_time = end_time - start_time
                
                result = {
                    'task_id': task_id,
                    'success': process.returncode == 0,
                    'returncode': process.returncode,
                    'stdout': stdout,
                    'stderr': stderr,
                    'execution_time': execution_time,
                    'command': ' '.join(cmd),
                    'output_dir': output_dir
                }
                
                # 清理进程记录
                if task_id in self.running_processes:
                    del self.running_processes[task_id]
                
                # 调用完成回调
                if complete_callback:
                    complete_callback(result)
                    
            except Exception as e:
                result = {
                    'task_id': task_id,
                    'success': False,
                    'error': str(e),
                    'execution_time': 0
                }
                
                if complete_callback:
                    complete_callback(result)
        
        # 在新线程中运行任务
        thread = threading.Thread(target=run_task)
        thread.daemon = True
        thread.start()
        
        return thread
    
    def stop_task(self, task_id):
        """停止运行中的任务"""
        if task_id in self.running_processes:
            try:
                process = self.running_processes[task_id]
                process.terminate()
                
                # 等待进程结束，如果超时则强制杀死
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
                
                del self.running_processes[task_id]
                return True, "任务已停止"
                
            except Exception as e:
                return False, f"停止任务失败: {e}"
        else:
            return False, "任务不存在或已完成"
    
    def get_running_tasks(self):
        """获取正在运行的任务列表"""
        return list(self.running_processes.keys())
    
    def is_task_running(self, task_id):
        """检查任务是否正在运行"""
        return task_id in self.running_processes

class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.tasks = {}
        self.task_history = []
        
    def create_task(self, fmw_path, parameters=None, output_dir=None):
        """创建新任务"""
        task_id = f"task_{int(time.time() * 1000)}"
        
        task = {
            'id': task_id,
            'fmw_path': fmw_path,
            'fmw_name': os.path.basename(fmw_path),
            'parameters': parameters or {},
            'output_dir': output_dir,
            'status': 'created',
            'created_at': datetime.now().isoformat(),
            'started_at': None,
            'completed_at': None,
            'execution_time': 0,
            'result': None,
            'progress': []
        }
        
        self.tasks[task_id] = task
        return task_id
    
    def update_task_status(self, task_id, status, result=None):
        """更新任务状态"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task['status'] = status
            
            if status == 'running' and not task['started_at']:
                task['started_at'] = datetime.now().isoformat()
            elif status in ['completed', 'failed', 'stopped']:
                task['completed_at'] = datetime.now().isoformat()
                if result:
                    task['result'] = result
                    task['execution_time'] = result.get('execution_time', 0)
                
                # 移动到历史记录
                self.task_history.append(task.copy())
                if len(self.task_history) > 100:  # 保留最近100条记录
                    self.task_history.pop(0)
    
    def add_task_progress(self, task_id, message):
        """添加任务进度信息"""
        if task_id in self.tasks:
            self.tasks[task_id]['progress'].append({
                'timestamp': datetime.now().isoformat(),
                'message': message
            })
    
    def get_task(self, task_id):
        """获取任务信息"""
        return self.tasks.get(task_id)
    
    def get_all_tasks(self):
        """获取所有任务"""
        return self.tasks
    
    def get_task_history(self):
        """获取任务历史"""
        return self.task_history
