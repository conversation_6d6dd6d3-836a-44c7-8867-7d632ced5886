import xml.etree.ElementTree as ET
import json
import os
import base64
import sys
from typing import Dict, List, Any, Tuple
from collections import OrderedDict
import logging
import re

logger = logging.getLogger(__name__)

def get_base_dir() -> str:
    """获取基础目录路径"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe，使用exe所在目录
        return os.path.dirname(sys.executable)
    else:
        # 如果是开发环境，使用当前文件所在目录
        return os.path.dirname(os.path.abspath(__file__))

def parse_fmw_model(fmw_path: str) -> Dict[str, Any]:
    """
    解析FMW模型文件
    
    Args:
        fmw_path: FMW文件的完整路径
        
    Returns:
        包含模型信息的字典
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(fmw_path):
            raise FileNotFoundError(f"FMW文件不存在: {fmw_path}")
            
        # 解析XML文件
        tree = ET.parse(fmw_path)
        root = tree.getroot()
        
        # 获取模型信息
        model_info = {
            "name": os.path.basename(fmw_path),
            "path": fmw_path,
            "parameters": [],
            "tools": []
        }
        
        # 查找所有工具节点
        tools = root.findall(".//Tool")
        for tool in tools:
            tool_info = {
                "name": tool.get("Name", ""),
                "type": tool.get("Type", ""),
                "parameters": []
            }
            
            # 获取工具的参数
            parameters = tool.findall(".//Parameter")
            for param in parameters:
                param_info = {
                    "name": param.get("Name", ""),
                    "type": param.get("DataType", ""),
                    "required": param.get("Required", "false").lower() == "true",
                    "value": param.get("Value", "")
                }
                tool_info["parameters"].append(param_info)
                
            model_info["tools"].append(tool_info)
            
        return model_info
        
    except Exception as e:
        print(f"解析FMW模型时出错: {str(e)}")
        return None

def get_component_info(param_type: str, param_info: Dict) -> Dict:
    """
    根据参数类型返回对应的前端组件信息
    """
    component_map = {
        'message': {
            'type': 'el-alert',
            'props': {
                'type': 'info',
                'show-icon': True
            }
        },
        'dropdown': {
            'type': 'el-select',
            'props': {
                'placeholder': '请选择',
                'style': 'width: 100%'
            }
        },
        'listbox': {
            'type': 'el-select',
            'props': {
                'multiple': True,
                'collapse-tags': True,
                'placeholder': '请选择',
                'style': 'width: 100%'
            }
        },
        'color': {
            'type': 'el-color-picker',
            'props': {
                'show-alpha': True,
                'colorFormat': 'rgb',
                'showPicker': True,
                'size': 'default'
            }
        },
        'datetime': {
            'type': 'el-date-picker',
            'props': {
                'type': 'datetime',
                'placeholder': '请选择日期时间',
                'style': 'width: 100%',
                'value-format': 'YYYY-MM-DD HH:mm:ss'
            }
        },
        'file': {
            'type': 'el-upload',
            'props': {
                'action': '/api/upload',
                'multiple': False,
                'show-file-list': True
            }
        },
        'text': {
            'type': 'el-input',
            'props': {
                'placeholder': '请输入',
                'clearable': True
            }
        },
        'textarea': {
            'type': 'el-input',
            'props': {
                'type': 'textarea',
                'rows': 4,
                'placeholder': '请输入',
                'clearable': True
            }
        },
        'number': {
            'type': 'el-input-number',
            'props': {
                'placeholder': '请输入数字'
            }
        },
        'radio': {
            'type': 'el-radio-group',
            'props': {}
        },
        'switch': {
            'type': 'el-switch',
            'props': {}
        },
        'slider': {
            'type': 'el-slider',
            'props': {}
        },
        'rate': {
            'type': 'el-rate',
            'props': {}
        },
        'password': {
            'type': 'el-input',
            'props': {
                'type': 'password',
                'placeholder': '请输入密码'
            }
        },
        'image': {
            'type': 'el-upload',
            'props': {
                'action': '/api/upload',
                'list-type': 'picture-card',
                'show-file-list': True
            }
        },
        'json': {
            'type': 'el-input',
            'props': {
                'type': 'textarea',
                'rows': 6,
                'placeholder': '请输入JSON字符串',
                'clearable': True
            }
        },
        'tags': {
            'type': 'el-select',
            'props': {
                'multiple': True,
                'collapse-tags': True,
                'placeholder': '请选择',
                'style': 'width: 100%'
            }
        },
        'cascader': {
            'type': 'el-cascader',
            'props': {
                'placeholder': '请选择',
                'style': 'width: 100%'
            }
        },
        'tree': {
            'type': 'el-tree-select',
            'props': {
                'placeholder': '请选择',
                'style': 'width: 100%'
            }
        },
        'editor': {
            'type': 'el-input',  # 可替换为富文本组件
            'props': {
                'type': 'textarea',
                'rows': 8,
                'placeholder': '请输入内容',
                'clearable': True
            }
        },
        'checkbox': {
            'type': 'el-checkbox',
            'props': {
                'label': param_info.get('prompt', '')
            }
        },
        'group': {
            'type': 'el-collapse',
            'props': {
                'accordion': True
            }
        }
    }
    
    # 获取基础组件信息
    component = component_map.get(param_type, {
        'type': 'el-input',
        'props': {
            'placeholder': '请输入',
            'clearable': True
        }
    })
    
    # 根据参数特性调整组件属性
    if param_type == 'file':
        if param_info.get('accessMode') == 'read':
            component['props']['disabled'] = True
        if param_info.get('itemsToSelect') == 'folders':
            component['props']['directory'] = True
            # 如果是文件夹，限制只能上传压缩包
            component['props']['accept'] = '.zip,.rar,.7z'
            component['props']['file_types'] = ['.zip', '.rar', '.7z']
        else:
            # 处理文件类型
            if 'file_types' in param_info:
                component['props']['accept'] = ','.join(param_info['file_types'])
            else:
                filters = param_info.get('filters', [])
                if filters:
                    # 提取所有文件扩展名
                    extensions = []
                    for filter_item in filters:
                        if 'filter' in filter_item:
                            for ext in filter_item['filter']:
                                if ext.startswith('*.'):
                                    extensions.append(ext[1:])  # 去掉*号
                    if extensions:
                        component['props']['accept'] = ','.join(extensions)
                        component['props']['file_types'] = extensions
    
    # 处理listbox特有属性
    if param_type == 'listbox':
        if not param_info.get('singleSelection', False):
            component['props']['multiple'] = True
            component['props']['collapse-tags'] = True
        if 'delimiter' in param_info:
            component['props']['delimiter'] = param_info['delimiter']
    
    return component

def parse_fmw_parameters(fmw_path: str, debug: bool = False) -> Dict[str, Any]:
    """
    解析FMW文件中的用户参数，并自动检测条件显示（visibility）
    
    Args:
        fmw_path: FMW文件的完整路径
        debug: 是否输出详细日志，默认为False
    """
    try:
        if debug:
            logger.info(f"\n=== 开始解析FMW文件 ===")
            logger.info(f"文件路径: {fmw_path}")
        
        # 修正：只有在fmw_path不是绝对路径时，才拼接cadtogis路径
        if 'cadtogis' in fmw_path.lower():
            if not os.path.isabs(fmw_path):
                current_dir = os.path.dirname(os.path.abspath(__file__))
                fmw_path = os.path.join(current_dir, 'tools', 'cadtogis', 'cad2gis.fmw')
                if debug:
                    logger.info(f"检测到CADToGIS工具请求，使用固定路径: {fmw_path}")
            else:
                if debug:
                    logger.info(f"检测到CADToGIS工具请求，使用传入的绝对路径: {fmw_path}")
        
        if not os.path.exists(fmw_path):
            error_msg = f"FMW文件不存在: {fmw_path}"
            if debug:
                logger.error(f"错误: {error_msg}")
            raise FileNotFoundError(error_msg)
            
        if debug:
            logger.info("文件存在，开始读取内容...")
        try:
            with open(fmw_path, 'r', encoding='utf-8') as f:
                content = f.read()
            if debug:
                logger.info(f"文件内容读取成功，长度: {len(content)} 字符")
        except Exception as e:
            error_msg = f"读取文件失败: {str(e)}"
            if debug:
                logger.error(f"错误: {error_msg}")
            raise ValueError(error_msg)
            
        if debug:
            logger.info("开始查找参数标签...")
        # 首先尝试查找USER_PARAMETERS标签
        start = content.find('<USER_PARAMETERS')
        if start == -1:
            if debug:
                logger.info("未找到USER_PARAMETERS标签，尝试查找WORKSPACE_PARAMETERS...")
            # 如果没有找到USER_PARAMETERS，尝试查找WORKSPACE_PARAMETERS
            start = content.find('<WORKSPACE_PARAMETERS')
            if start == -1:
                error_msg = "未找到参数标签(USER_PARAMETERS或WORKSPACE_PARAMETERS)"
                if debug:
                    logger.error(f"错误: {error_msg}")
                raise ValueError(error_msg)
                
        if debug:
            logger.info(f"找到参数标签，位置: {start}")
            logger.info("开始查找FORM属性...")
        
        form_start = content.find('FORM="', start)
        if form_start == -1:
            error_msg = "未找到FORM属性"
            if debug:
                logger.error(f"错误: {error_msg}")
            raise ValueError(error_msg)
            
        form_start += 6  # 跳过'FORM="'
        form_end = content.find('"', form_start)
        if form_end == -1:
            error_msg = "FORM属性格式错误，找不到结束引号"
            if debug:
                logger.error(f"错误: {error_msg}")
            raise ValueError(error_msg)
            
        base64_params = content[form_start:form_end]
        if debug:
            logger.info(f"获取到Base64编码的参数，长度: {len(base64_params)}")
        
        try:
            decoded_params = base64.b64decode(base64_params).decode('utf-8')
            if debug:
                logger.info("Base64解码成功")
                logger.info(f"解码后的参数长度: {len(decoded_params)}")
        except Exception as decode_error:
            error_msg = f"Base64解码失败: {str(decode_error)}"
            if debug:
                logger.error(f"错误: {error_msg}")
                logger.error(f"Base64内容前50个字符: {base64_params[:50]}...")
            raise ValueError(error_msg)
            
        try:
            params = json.loads(decoded_params)
            if debug:
                logger.info("JSON解析成功")
                logger.info(f"解析到的参数数量: {len(params.get('parameters', []))}")
        except json.JSONDecodeError as json_error:
            error_msg = f"JSON解析失败: {str(json_error)}"
            if debug:
                logger.error(f"错误: {error_msg}")
                logger.error(f"JSON内容前100个字符: {decoded_params[:100]}...")
            raise ValueError(error_msg)
        
        # 提取参数信息，使用OrderedDict保持原始顺序
        parameters_list = []
        parameters_dict = OrderedDict()
        
        def process_parameter(param: Dict, parent_name: str = "") -> Dict:
            """处理单个参数及其子参数"""
            param_name = param.get('name', '')
            if not param_name:
                return None
                
            if debug:
                logger.info(f"\n处理参数: {param_name}")
            
            param_info = {
                'type': param.get('type', 'text'),
                'prompt': param.get('prompt', param_name),
                'required': param.get('required', False),
                'default_value': param.get('defaultValue', ''),
                'value_type': param.get('valueType', ''),
                'access_mode': param.get('accessMode', 'read'),
                'order': len(parameters_list),
                'options': [],
                'parameters': []  # 用于存储子参数
            }
            
            if debug:
                logger.info(f"参数类型: {param_info['type']}")
                logger.info(f"是否必填: {param_info['required']}")
            
            # 处理子参数
            if 'parameters' in param:
                for sub_param in param.get('parameters', []):
                    sub_param_info = process_parameter(sub_param, param_name)
                    if sub_param_info:
                        param_info['parameters'].append(sub_param_info)
            
            # 处理下拉选项
            if param_info['type'] in ['dropdown', 'listbox']:
                choice_settings = param.get('choiceSettings', {})
                choices = choice_settings.get('choices', [])
                if choices:
                    param_info['options'] = [
                        {
                            'label': choice.get('display', ''),
                            'value': choice.get('value', '')
                        }
                        for choice in choices
                    ]
                    if debug:
                        logger.info(f"下拉选项数量: {len(param_info['options'])}")
                
                    # 处理listbox特有属性
                    if param_info['type'] == 'listbox':
                        param_info['delimiter'] = param.get('delimiter', ',')
                        param_info['singleSelection'] = param.get('singleSelection', False)
            
            # 处理文件类型参数
            if param_info['type'] == 'file':
                param_info['itemsToSelect'] = param.get('itemsToSelect', 'files')
                if param_info['itemsToSelect'] == 'folders':
                    param_info['is_folder'] = True
                    param_info['file_types'] = ['.zip', '.rar', '.7z']  # 文件夹只允许压缩包
                else:  # files
                    param_info['is_folder'] = False
                    param_info['filters'] = param.get('filters', [])
                    param_info['selectMultiple'] = param.get('selectMultiple', False)
                    
                    # 添加文件类型信息
                    if param_info['filters']:
                        file_types = []
                        for filter_item in param_info['filters']:
                            if 'filter' in filter_item:
                                file_types.extend(filter_item['filter'])
                        param_info['file_types'] = list(set(file_types))  # 去重
                    else:
                        param_info['file_types'] = ['*.*']  # 默认允许所有文件类型
            
            # 处理visibility条件
            visibility = param.get('visibility', {})
            if visibility:
                if debug:
                    logger.info(f"处理visibility条件")
                
                # 处理简单的visibility字符串值
                if isinstance(visibility, str):
                    param_info['visibility'] = {
                        'if': [{
                            'condition': {},
                            'then': visibility
                        }]
                    }
                    if debug:
                        logger.info(f"简单visibility值: {visibility}")
                # 处理复杂的visibility对象
                elif isinstance(visibility, dict):
                    if 'if' in visibility and isinstance(visibility['if'], list):
                        param_info['visibility'] = {
                            'if': []
                        }
                        for cond in visibility['if']:
                            condition = {}
                            if '$allOf' in cond:
                                all_of_conditions = []
                                for sub_cond in cond['$allOf']:
                                    if '$equals' in sub_cond:
                                        eq = sub_cond['$equals']
                                        all_of_conditions.append({
                                            'equals': {
                                                'parameter': eq['parameter'],
                                                'value': eq['value']
                                            }
                                        })
                                    elif '$isEnabled' in sub_cond:
                                        en = sub_cond['$isEnabled']
                                        all_of_conditions.append({
                                            'isEnabled': {
                                                'parameter': en['parameter']
                                            }
                                        })
                                condition['allOf'] = all_of_conditions
                            elif '$equals' in cond:
                                eq = cond['$equals']
                                condition['equals'] = {
                                    'parameter': eq['parameter'],
                                    'value': eq['value']
                                }
                            elif '$isEnabled' in cond:
                                en = cond['$isEnabled']
                                condition['isEnabled'] = {
                                    'parameter': en['parameter']
                                }
                            
                            param_info['visibility']['if'].append({
                                'condition': condition,
                                'then': cond.get('then', 'visibleEnabled')
                            })
                        if debug:
                            logger.info(f"visibility条件数量: {len(param_info['visibility']['if'])}")
            
            # 添加组件信息
            param_info['component'] = get_component_info(param_info['type'], param_info)
            if debug:
                logger.info(f"组件类型: {param_info['component']['type']}")
            
            return {
                "name": param_name,
                "info": param_info
            }
        
        if debug:
            logger.info("\n开始处理参数列表...")
        # 处理参数列表
        for param in params.get('parameters', []):
            param_info = process_parameter(param)
            if param_info:
                parameters_list.append(param_info)
                parameters_dict[param_info['name']] = param_info['info']
        
        if not parameters_list and debug:
            logger.warning("警告: 未找到任何参数")
        
        result = {
            "path": fmw_path,
            "name": os.path.basename(fmw_path),
            "parameters": parameters_list,
            "parameters_dict": parameters_dict
        }
        
        if debug:
            logger.info(f"\n=== 解析完成 ===")
            logger.info(f"成功解析到 {len(parameters_list)} 个参数")
            logger.info("==================\n")
        return result
        
    except Exception as e:
        error_msg = f"解析FMW参数时出错: {str(e)}"
        if debug:
            logger.error(f"\n错误: {error_msg}")
            logger.error("==================\n")
        raise ValueError(error_msg)

def parse_visibility_condition(condition: Dict) -> str:
    """
    解析visibility条件，返回Vue v-if表达式
    """
    if 'allOf' in condition:
        exprs = []
        for sub in condition['allOf']:
            if 'equals' in sub:
                eq = sub['equals']
                exprs.append(f"form.{eq['parameter']} === '{eq['value']}'")
            elif 'isEnabled' in sub:
                en = sub['isEnabled']
                exprs.append(f"form.{en['parameter']}")
        return ' && '.join(exprs)
    elif 'equals' in condition:
        eq = condition['equals']
        return f"form.{eq['parameter']} === '{eq['value']}'"
    elif 'isEnabled' in condition:
        en = condition['isEnabled']
        return f"form.{en['parameter']}"
    return ""

def format_visibility_action(action: str) -> str:
    """
    格式化visibility动作说明
    """
    action_map = {
        'visibleEnabled': '显示并启用',
        'visibleDisabled': '显示但禁用',
        'hiddenEnabled': '隐藏但启用',
        'hiddenDisabled': '隐藏并禁用'
    }
    return action_map.get(action, action)

def main():
    fixed_fmw = r"E:\GeoStream_Integration\frontend\fmw参数解析.fmw"
    print("开始执行主程序...")
    params = parse_fmw_parameters(fixed_fmw)
    
    if params:
        print("\n参数信息:")
        for name, info in params.items():
            print(f"\n参数名称: {name}")
            print(f"参数类型: {info['type']}")
            print(f"参数提示: {info['prompt']}")
            print(f"是否必填: {info['required']}")
            print(f"默认值: {info['value']}")
            print(f"值类型: {info['value_type']}")
            print(f"访问模式: {info['access_mode']}")
            print(f"前端组件: {info['component']['type']}")
            print(f"组件属性: {json.dumps(info['component']['props'], ensure_ascii=False)}")
            
            if info['type'] == 'dropdown' and 'options' in info:
                print("选项列表:")
                for option in info['options']:
                    print(f"  - {option}")
            
            # 解析并输出visibility条件
            if 'visibility' in info:
                vis = info['visibility']
                print("\n【条件显示自动解析】")
                if 'if' in vis and isinstance(vis['if'], list):
                    for idx, cond in enumerate(vis['if']):
                        vue_if = parse_visibility_condition(cond)
                        if vue_if:
                            print(f"\n条件{idx+1}:")
                            print(f"v-if表达式: {vue_if}")
                            print(f"条件说明: 当 {vue_if} 时")
                        if 'then' in cond:
                            print(f"执行动作: {format_visibility_action(cond['then'])}")
                else:
                    print(f"未能自动解析的visibility结构: {json.dumps(vis, ensure_ascii=False)}")
    else:
        print("解析参数失败")

if __name__ == "__main__":
    main() 